/**
 * 滴答清单 API 接口封装
 * 提供统一的滴答清单相关功能调用
 */

/**
 * 滴答清单密码登录
 * @param {string} username 用户名（邮箱或手机号）
 * @param {string} password 密码
 * @returns {Promise<object>} 登录结果
 */
export async function didaPasswordLogin(username, password) {
  try {
    const didaApi = uniCloud.importObject('dida-api')
    const result = await didaApi.passwordLogin(username, password)
    
    if (result.errCode) {
      throw new Error(result.errMsg)
    }
    
    return result.data
  } catch (error) {
    console.error('滴答清单登录失败:', error)
    throw error
  }
}

/**
 * 检查滴答清单登录状态
 * @returns {boolean} 是否已登录
 */
export function isDidaLoggedIn() {
  const token = uni.getStorageSync('dida_token')
  const userInfo = uni.getStorageSync('dida_user_info')
  return !!(token && userInfo)
}

/**
 * 获取滴答清单用户信息
 * @returns {object|null} 用户信息
 */
export function getDidaUserInfo() {
  return uni.getStorageSync('dida_user_info') || null
}

/**
 * 获取滴答清单认证令牌
 * @returns {string|null} 认证令牌
 */
export function getDidaToken() {
  return uni.getStorageSync('dida_token') || null
}

/**
 * 清除滴答清单登录信息
 */
export function clearDidaLoginInfo() {
  uni.removeStorageSync('dida_token')
  uni.removeStorageSync('dida_user_info')
}

/**
 * 保存滴答清单登录信息
 * @param {object} userInfo 用户信息
 */
export function saveDidaLoginInfo(userInfo) {
  uni.setStorageSync('dida_token', userInfo.token)
  uni.setStorageSync('dida_user_info', userInfo)
}
