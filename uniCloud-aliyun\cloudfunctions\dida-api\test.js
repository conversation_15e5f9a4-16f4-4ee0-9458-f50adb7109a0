/**
 * 滴答清单 API 云函数测试脚本
 * 用于本地测试云函数功能
 */

// 模拟 uniCloud 环境（仅用于本地测试）
const mockUniCloud = {
  httpclient: {
    async request(url, options) {
      console.log('模拟 HTTP 请求:', { url, options })
      
      // 模拟成功响应
      return {
        status: 200,
        data: {
          token: 'mock_token_12345',
          userId: 'mock_user_id',
          userCode: 'mock_user_code',
          username: '<EMAIL>',
          inboxId: 'mock_inbox_id',
          pro: true,
          teamPro: false,
          proStartDate: '2024-01-01',
          proEndDate: '2024-12-31',
          subscribeType: 'wxpay_subscribe',
          subscribeFreq: 'Month',
          needSubscribe: false,
          teamUser: false,
          activeTeamUser: false,
          freeTrial: false,
          gracePeriod: false,
          ds: false
        }
      }
    }
  }
}

// 导入云函数模块
const cloudFunction = require('./index.obj.js')

// 模拟云函数上下文
const mockContext = {
  uniCloud: mockUniCloud,
  getClientInfo: () => ({}),
  getUniCloudRequestId: () => 'mock_request_id'
}

// 绑定上下文
const boundFunction = {
  _before: cloudFunction._before.bind(mockContext),
  passwordLogin: cloudFunction.passwordLogin.bind(mockContext)
}

// 测试函数
async function testPasswordLogin() {
  console.log('=== 开始测试滴答清单密码登录 ===\n')
  
  // 测试用例1：正常登录
  console.log('测试用例1：正常登录')
  try {
    const result1 = await boundFunction.passwordLogin('<EMAIL>', 'password123')
    console.log('结果:', JSON.stringify(result1, null, 2))
  } catch (error) {
    console.error('错误:', error)
  }
  
  console.log('\n' + '='.repeat(50) + '\n')
  
  // 测试用例2：缺少用户名
  console.log('测试用例2：缺少用户名')
  try {
    const result2 = await boundFunction.passwordLogin('', 'password123')
    console.log('结果:', JSON.stringify(result2, null, 2))
  } catch (error) {
    console.error('错误:', error)
  }
  
  console.log('\n' + '='.repeat(50) + '\n')
  
  // 测试用例3：缺少密码
  console.log('测试用例3：缺少密码')
  try {
    const result3 = await boundFunction.passwordLogin('<EMAIL>', '')
    console.log('结果:', JSON.stringify(result3, null, 2))
  } catch (error) {
    console.error('错误:', error)
  }
  
  console.log('\n' + '='.repeat(50) + '\n')
  
  // 测试用例4：参数为 null
  console.log('测试用例4：参数为 null')
  try {
    const result4 = await boundFunction.passwordLogin(null, null)
    console.log('结果:', JSON.stringify(result4, null, 2))
  } catch (error) {
    console.error('错误:', error)
  }
  
  console.log('\n=== 测试完成 ===')
}

// 运行测试
if (require.main === module) {
  testPasswordLogin().catch(console.error)
}

module.exports = {
  testPasswordLogin
}
