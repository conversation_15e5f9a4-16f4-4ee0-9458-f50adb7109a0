# 今日页面 KR 卡片进度条背景需求

status: draft

## 背景

当前今日页面展示的 KR 卡片无法直观地看到完成的进度，用户需要通过文字信息来了解任务的完成情况。为了提升用户体验，使任务进度更加直观可见，需要在卡片中添加背景进度条效果，通过视觉方式直接展示任务完成程度。

## 需求

### 功能需求

1. 在今日页面的 KR 卡片中添加进度条背景效果
2. 进度条应从左向右填充，填充比例对应任务的完成进度
3. 进度条应使用适当的颜色，与现有 UI 风格保持一致
4. 对于重复任务，进度条应反映当前周期的完成情况，而非仅当天的完成情况
5. 对于普通任务，进度条应反映总体完成情况
6. 保留现有的文字进度显示，作为进度条的补充信息

### 非功能需求

1. 进度条动画应流畅，不影响页面性能
2. 进度条颜色应考虑深色/浅色模式的适配
3. 进度条样式应保持一致的视觉风格

## 技术方案

### 实现思路

1. 在 task-item 组件中添加进度条背景层
2. 修改 `getCustomRepeatProgressText` 函数，使其在返回文本的同时计算并添加进度百分比属性到任务对象
3. 根据不同的循环类型，计算相应的周期完成进度
4. 使用 CSS 变量定义进度条颜色，便于主题适配
5. 使用绝对定位确保进度条在内容下方不影响布局

### 代码实现

在`src/pages/okr/today.vue`文件中，修改 task-item 相关样式和结构：

1. 修改 task-item 组件结构，添加进度条背景层：

```html
<view class="task-item" @click="goToKrDetail(task)">
  <view class="task-progress-bg" :style="{ width: task.progressPercent + '%' }"></view>
  <!-- 现有内容保持不变 -->
  <view class="task-header">
    <!-- 现有内容 -->
  </view>
  <view class="task-details">
    <!-- 现有内容 -->
  </view>
</view>
```

2. 修改 `getCustomRepeatProgressText` 函数，添加进度百分比计算：

```javascript
// 获取自定义循环任务进度文本并计算进度百分比
const getCustomRepeatProgressText = (task) => {
  // 初始化进度百分比为0
  task.progressPercent = 0

  // 检查任务是否有 progressData
  if (!task.progressData || !task.repeatFlag) {
    // 对于非重复任务，使用总体进度
    if (!task.repeatFlag) {
      task.progressPercent = task.progress || 0
    }
    return ''
  }

  const rule = typeof task.repeatFlag === 'string' ? rrule.parseRule(task.repeatFlag) : task.repeatFlag
  const progressData = task.progressData
  const dailyTarget = task.dailyTarget || 0

  // 根据不同循环类型计算进度百分比
  // 基础循环类型：DAILY, WEEKLY, MONTHLY, INTERVAL_DAILY
  if (['DAILY', 'WEEKLY', 'MONTHLY', 'INTERVAL_DAILY'].includes(rule.type)) {
    if (!progressData.isOccurrenceToday) return ''
    // 计算当天进度百分比
    task.progressPercent = Math.min((progressData.completedToday / dailyTarget) * 100, 100)
    return `今天:${progressData.completedToday}/${dailyTarget}`
  }

  // 周期内完成类型 - WEEKLY_N_TIMES, MONTHLY_N_TIMES
  if (rule.type === 'WEEKLY_N_TIMES') {
    if (!progressData.isOccurrenceToday) return ''
    // 计算本周进度百分比
    task.progressPercent = Math.min((progressData.completedDays / progressData.totalDays) * 100, 100)
    return `本周:${progressData.completedDays}/${progressData.totalDays}  今天:${progressData.completedToday}/${dailyTarget}`
  }

  if (rule.type === 'MONTHLY_N_TIMES') {
    if (!progressData.isOccurrenceToday) return ''
    // 计算本月进度百分比
    task.progressPercent = Math.min((progressData.completedDays / progressData.totalDays) * 100, 100)
    return `本月：${progressData.completedDays}/${progressData.totalDays}  今天：${progressData.completedToday}/${dailyTarget}`
  }

  // 周期内完成类型 - N_DAYS, N_WEEKS, N_MONTHS
  if (rule.type === 'N_DAYS') {
    // 计算N天内的进度百分比
    task.progressPercent = Math.min((progressData.completedCount / progressData.totalCount) * 100, 100)
    return `第${progressData.dayOfPeriod}天  ${progressData.totalDays}天内：${progressData.completedCount}/${progressData.totalCount}`
  }

  if (rule.type === 'N_WEEKS') {
    // 计算N周内的进度百分比
    task.progressPercent = Math.min((progressData.completedCount / progressData.totalCount) * 100, 100)
    return `第${progressData.weekOfPeriod}周  ${progressData.totalWeeks}周内：${progressData.completedCount}/${progressData.totalCount}`
  }

  if (rule.type === 'N_MONTHS') {
    // 计算N月内的进度百分比
    task.progressPercent = Math.min((progressData.completedCount / progressData.totalCount) * 100, 100)
    return `第${progressData.monthOfPeriod}月  ${progressData.totalMonths}月内：${progressData.completedCount}/${progressData.totalCount}`
  }

  return ''
}
```

3. 添加进度条样式：

```css
.task-item {
  position: relative;
  overflow: hidden; /* 确保进度条不超出卡片边界 */
  /* 保留现有样式 */
}

.task-progress-bg {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: var(--color-primary-transparent-10);
  z-index: 1;
  transition: width 0.3s ease;
}

/* 确保内容在进度条上层 */
.task-header,
.task-details {
  position: relative;
  z-index: 2;
}
```

### 视觉效果

进度条将以半透明的主题色填充卡片背景，从左向右增长，直观展示任务完成程度。卡片内容保持在进度条上层，确保可读性。

## 风险评估

### 假设与未知因素

- 假设现有的进度计算逻辑准确无误
- 假设进度条不会影响现有布局和交互

### 潜在风险

- 进度条可能在某些设备上显示异常
- 进度条颜色可能在某些主题下对比度不足
- 解决方案：进行充分的跨设备测试，确保进度条在各种设备和主题下都有良好的可见度
