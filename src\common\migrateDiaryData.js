import db from '@/api/database/index'
import dayjs from 'dayjs'

/**
 * 将日记的 AI 分析结果从多条记录转换为单个 JSON 字段
 * @returns {Promise<{success: boolean, message?: string, error?: any}>}
 */
export const migrateDiaryData = async () => {
  try {
    console.log('开始数据迁移...')

    // 获取所有日记记录
    const allDiaries = await db.table('memo').where('type == "diary"').toArray()

    console.log(`找到 ${allDiaries.length} 条日记记录`)

    // 为每条日记处理 AI 分析内容
    for (const diary of allDiaries) {
      // 获取该日记的所有 AI 分析记录
      const aiRecords = await db.table('memo').where(`type == "aiDiary" && date == "${diary.date}"`).toArray()

      if (aiRecords.length === 0) {
        console.log(`日记 ${diary._id} (${diary.date}) 没有 AI 分析记录，跳过`)
        continue
      }

      // 构建 AI 分析 JSON
      const aiAnalysis = {}
      const tagsList = []

      for (const record of aiRecords) {
        if (record.parentId === 'title') {
          aiAnalysis['title'] = record.content
        } else {
          // 查找标签，使用 tag._id 作为键
          const tag = await db.table('memo').get(record.parentId)
          if (tag && tag.content) {
            aiAnalysis[record.parentId] = record.content
            tagsList.push(record.parentId)
          }
        }
      }

      // 更新日记记录
      await db.table('memo').update(diary._id, {
        aiAnalysis: JSON.stringify(aiAnalysis),
        tagsList: JSON.stringify(tagsList),
      })

      console.log(`日记 ${diary._id} (${diary.date}) 迁移完成，包含 ${tagsList.length} 个标签`)
    }

    console.log('数据迁移完成！')
    return { success: true, message: '迁移成功' }
  } catch (error) {
    console.error('数据迁移失败：', error)
    return { success: false, error }
  }
}

/**
 * 检查并提示用户是否执行数据迁移
 */
export const checkAndMigrate = async () => {
  // 直接弹窗询问用户是否升级
  uni.showModal({
    title: '数据升级提示',
    content: '检测到日记模块需要升级数据结构，是否立即升级？',
    success: async (res) => {
      if (res.confirm) {
        // 用户确认升级
        uni.showLoading({ title: '正在升级数据，请稍候...', mask: true })

        try {
          // 执行迁移
          const result = await migrateDiaryData()

          if (result.success) {
            // 更新版本记录
            uni.setStorageSync('diary_data_version', '2.0.0')
            uni.hideLoading()
            uni.showToast({ title: '数据升级完成', icon: 'success' })
            cleanupMigration()
          } else {
            uni.hideLoading()
            uni.showModal({
              title: '数据升级失败',
              content: '请联系客服处理',
              showCancel: false,
            })
          }
        } catch (e) {
          uni.hideLoading()
          uni.showModal({
            title: '数据升级异常',
            content: '请重启应用或联系客服',
            showCancel: false,
          })
        }
      } else {
        // 用户取消升级
        uni.showToast({ title: '您取消了数据升级', icon: 'none' })
      }
    },
  })
}

/**
 * 清理旧版 AI 分析数据（仅在确认新版本稳定后使用）
 * @returns {Promise<{success: boolean, error?: any}>}
 */
export const cleanupMigration = async () => {
  // 待所有功能都确认正常后，可以清理旧版数据
  try {
    // 删除所有类型为 aiDiary 的记录
    await db.table('memo').where('type == "aiDiary"').delete()
    console.log('旧版 AI 分析数据清理完成')
    return { success: true }
  } catch (error) {
    console.error('清理旧数据失败：', error)
    return { success: false, error }
  }
}
