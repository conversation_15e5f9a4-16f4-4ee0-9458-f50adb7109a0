# 提交周目标调用提取接口
**入参**
```json
{
  "content": "周目标内容（用户输入的文本）",
  "date": "YYYY-WW（周数格式，如 2023-25）"
}
```

**出参**
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "weekGoals": [
      {
        "id": "goal_1",
        "content": "目标 1 内容",
        "progress": 0,
        "tasks": []
      },
      {
        "id": "goal_2",
        "content": "目标 2 内容",
        "progress": 0,
        "tasks": []
      }
    ],
    "rawContent": "原始周目标内容"
  }
}
```

# 提交日回顾调用接口
**入参**
```json
{
  "weekOverview": {
    "weekGoals": [
      {
        "id": "goal_1",
        "content": "目标 1 内容",
        "progress": 0,
        "tasks": []
      },
      {
        "id": "goal_2",
        "content": "目标 2 内容",
        "progress": 0,
        "tasks": []
      }
    ]
  },
  "dailyTasks": {
    "date": "YYYY-MM-DD",
    "completedTasks": [
      {
        "id": "task_1",
        "content": "任务 1 内容",
        "completed": true
      },
      {
        "id": "task_2",
        "content": "任务 2 内容",
        "completed": true
      }
    ]
  }
}
```

**出参**
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "weekGoals": [
      {
        "id": "goal_1",
        "content": "目标 1 内容",
        "progress": 50,
        "tasks": [
          {
            "id": "task_1",
            "content": "任务 1 内容",
            "completed": true,
            "date": "YYYY-MM-DD"
          }
        ]
      },
      {
        "id": "goal_2",
        "content": "目标 2 内容",
        "progress": 0,
        "tasks": []
      }
    ],
    "analysis": {
      "summary": "今日完成情况总结",
      "progressChange": "+25%",
      "recommendations": "基于完成情况的建议"
    }
  }
}
```         icon: 'none'
          });
          return;
        }
        
        try {
          loading.value = true;
          const memoType = type.value === 'goal' ? 'weekGoal' : 'weekReview';
          
          if (currentId.value) {
            // 更新已有记录
            await updateMemo({
              _id: currentId.value,
              content: content.value,
            });
          } else {
            // 创建新记录
            await addMemo({
              type: memoType,
              date: weekDate.value,
              content: content.value,
            });
          }
          
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          });
          
          // 返回上一页
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } catch (error) {
          console.error('保存失败:', error);
          uni.showToast({
            title: '保存失败',
            icon: 'none'
          });
        } finally {
          loading.value = false;
        }
      }
      ```

6.  **弹窗内容更新**:
    - 在 `/pages/okr/components/l-week-overview-modal.vue` 中实现数据加载逻辑。
    - 当弹窗打开或切换标签页时，加载对应的周目标或周复盘内容。
    - 使用 `memo` API 进行查询，示例代码：
      ```javascript
      import { getMemo } from '@/api/memo';
      
      const loadWeekGoal = async () => {
        try {
          goalLoading.value = true;
          const query = `type == "weekGoal" && date == "${currentWeek.value}"`;
          const result = await getMemo(query);
          
          if (result && result.length > 0) {
            weekGoal.value = result[0].content;
          } else {
            weekGoal.value = '';
          }
        } catch (error) {
          console.error('加载周目标失败:', error);
        } finally {
          goalLoading.value = false;
        }
      }
      
      const loadWeekReview = async () => {
        try {
          reviewLoading.value = true;
          const query = `type == "weekReview" && date == "${currentWeek.value}"`;
          const result = await getMemo(query);
          
          if (result && result.length > 0) {
            weekReview.value = result[0].content;
          } else {
            weekReview.value = '';
          }
        } catch (error) {
          console.error('加载周复盘失败:', error);
        } finally {
          reviewLoading.value = false;
        }
      }
      ```

7.  **在 tools.ts 中使用 getCurrentWeek**:
    - 在实际开发中，可以在各个组件中直接导入并使用 `getCurrentWeek` 函数：
      ```javascript
      import { getCurrentWeek } from '@/utils/tools';
      
      // 在组件中使用
      const currentWeek = ref(getCurrentWeek());
      ```
    - 在 `l-week-overview-modal.vue` 中，可以使用此函数获取当前周：
      ```javascript
      import { ref, onMounted } from 'vue';
      import { getCurrentWeek } from '@/utils/tools';
      
      const currentWeek = ref(getCurrentWeek());
      ```

## 验收标准
- 点击 `l-week-overview-modal.vue` 中的"编辑"按钮，能正确跳转到 `weekEdit.vue` 页面，并传递正确的参数。
- `weekEdit.vue` 能根据传入的参数正确加载已有的周目标/周复盘数据。
- 在 `weekEdit.vue` 中编辑内容并保存后，数据能正确存入数据库。
- 保存后能自动返回上一页，并且 `l-week-overview-modal.vue` 中能显示更新后的内容。
- 切换不同周的数据时，能正确加载对应周的周目标和周复盘内容。

## 依赖关系
- `task-weekly-planning-01`: 周概览弹窗 UI 扩展
- `task-weekly-planning-02`: 周编辑页面 UI 实现

## 注意事项
- 确保使用 `router` 工具进行页面跳转，而不是直接使用 uni 的原生导航 API。
- 数据库操作应处理可能的异常情况，并向用户提供适当的反馈。
- 加载数据时应显示加载状态，提升用户体验。
- 保存数据前应验证内容是否为空。
- 周数格式统一为 `YYYY-WW`，确保跨年度的周数处理正确。