# 滴答清单 API 文档

## 基础信息

- 基础 URL: `https://api.dida365.com`
- 认证方式：Bearer Token
- 请求头：`Authorization: Bearer {{token}}`

## API 接口

### 1. 获取任务详情

**接口路径**: `GET /open/v1/project/{projectId}/task/{taskId}`

**描述**: 根据项目 ID 和任务 ID 获取任务详情

**路径参数**:

- `projectId` (必需): 项目标识符 (string)
- `taskId` (必需): 任务标识符 (string)

**响应状态码**:

- `200`: 成功返回任务信息
- `401`: 未授权
- `403`: 禁止访问
- `404`: 未找到

**请求示例**:

```http
GET /open/v1/project/{{projectId}}/task/{{taskId}} HTTP/1.1
Host: api.dida365.com
Authorization: Bearer {{token}}
```

**响应示例**:

```json
{
  "id": "63b7bebb91c0a5474805fcd4",
  "isAllDay": true,
  "projectId": "6226ff9877acee87727f6bca",
  "title": "Task Title",
  "content": "Task Content",
  "desc": "Task Description",
  "timeZone": "America/Los_Angeles",
  "repeatFlag": "RRULE:FREQ=DAILY;INTERVAL=1",
  "startDate": "2019-11-13T03:00:00+0000",
  "dueDate": "2019-11-14T03:00:00+0000",
  "reminders": ["TRIGGER:P0DT9H0M0S", "TRIGGER:PT0S"],
  "priority": 1,
  "status": 0,
  "completedTime": "2019-11-13T03:00:00+0000",
  "sortOrder": 12345,
  "items": [
    {
      "id": "6435074647fd2e6387145f20",
      "status": 0,
      "title": "Item Title",
      "sortOrder": 12345,
      "startDate": "2019-11-13T03:00:00+0000",
      "isAllDay": false,
      "timeZone": "America/Los_Angeles",
      "completedTime": "2019-11-13T03:00:00+0000"
    }
  ]
}
```

**字段说明**:

- `id`: 任务唯一标识符
- `isAllDay`: 是否为全天任务
- `projectId`: 所属项目 ID
- `title`: 任务标题
- `content`: 任务内容
- `desc`: 任务描述
- `timeZone`: 时区
- `repeatFlag`: 重复规则 (RRULE 格式)
- `startDate`: 开始时间
- `dueDate`: 截止时间
- `reminders`: 提醒设置数组
- `priority`: 优先级 (数字)
- `status`: 任务状态 (0: 未完成)
- `completedTime`: 完成时间
- `sortOrder`: 排序顺序
- `items`: 子任务列表

### 2. 创建任务

**接口路径**: `POST /open/v1/task`

**描述**: 创建新任务

**请求参数**:

| 参数名                | 类型    | 必需 | 描述                                         |
| --------------------- | ------- | ---- | -------------------------------------------- |
| `title`               | string  | 是   | 任务标题                                     |
| `content`             | string  | 否   | 任务内容                                     |
| `desc`                | string  | 否   | 清单描述                                     |
| `isAllDay`            | boolean | 否   | 是否全天                                     |
| `startDate`           | date    | 否   | 开始日期时间，格式："yyyy-MM-dd'T'HH:mm:ssZ" |
| `dueDate`             | date    | 否   | 截止日期时间，格式："yyyy-MM-dd'T'HH:mm:ssZ" |
| `timeZone`            | string  | 否   | 时区                                         |
| `reminders`           | list    | 否   | 任务特定的提醒列表                           |
| `repeatFlag`          | string  | 否   | 任务重复规则                                 |
| `priority`            | integer | 否   | 任务优先级，默认为"0"                        |
| `sortOrder`           | integer | 否   | 任务排序                                     |
| `items`               | list    | 否   | 子任务列表                                   |
| `items.title`         | string  | 否   | 子任务标题                                   |
| `items.startDate`     | date    | 否   | 开始日期时间，格式："yyyy-MM-dd'T'HH:mm:ssZ" |
| `items.isAllDay`      | boolean | 否   | 是否全天                                     |
| `items.sortOrder`     | integer | 否   | 子任务排序                                   |
| `items.timeZone`      | string  | 否   | 开始时间的时区                               |
| `items.status`        | integer | 否   | 子任务完成状态                               |
| `items.completedTime` | date    | 否   | 完成时间，格式："yyyy-MM-dd'T'HH:mm:ssZ"     |

**响应状态码**:

- `200`: 成功
- `201`: 已创建
- `401`: 未授权
- `403`: 禁止访问
- `404`: 未找到

**请求示例**:

```http
POST /open/v1/task HTTP/1.1
Host: api.dida365.com
Content-Type: application/json
Authorization: Bearer {{token}}

{
    "title":"Task Title",
    "projectId":"6226ff9877acee87727f6bca"
}
```

**响应示例**:

```json
{
  "id": "63b7bebb91c0a5474805fcd4",
  "projectId": "6226ff9877acee87727f6bca",
  "title": "Task Title",
  "content": "Task Content",
  "desc": "Task Description",
  "isAllDay": true,
  "startDate": "2019-11-13T03:00:00+0000",
  "dueDate": "2019-11-14T03:00:00+0000",
  "timeZone": "America/Los_Angeles",
  "reminders": ["TRIGGER:P0DT9H0M0S", "TRIGGER:PT0S"],
  "repeatFlag": "RRULE:FREQ=DAILY;INTERVAL=1",
  "priority": 1,
  "status": 0,
  "completedTime": "2019-11-13T03:00:00+0000",
  "sortOrder": 12345,
  "items": [
    {
      "id": "6435074647fd2e6387145f20",
      "status": 1,
      "title": "Subtask Title",
      "sortOrder": 12345,
      "startDate": "2019-11-13T03:00:00+0000",
      "isAllDay": false,
      "timeZone": "America/Los_Angeles",
      "completedTime": "2019-11-13T03:00:00+0000"
    }
  ]
}
```

### 3. 更新任务

**接口路径**: `POST /open/v1/task/{taskId}`

**描述**: 更新现有任务

**路径参数**:

- `taskId` (必需): 任务标识符 (string)

**请求参数**:

| 参数名       | 类型    | 必需 | 描述                                         |
| ------------ | ------- | ---- | -------------------------------------------- |
| `id`         | string  | 是   | 任务 ID                                      |
| `projectId`  | string  | 是   | 项目 ID                                      |
| `title`      | string  | 否   | 任务标题                                     |
| `content`    | string  | 否   | 任务内容                                     |
| `desc`       | string  | 否   | 清单描述                                     |
| `isAllDay`   | boolean | 否   | 是否全天                                     |
| `startDate`  | date    | 否   | 开始日期时间，格式："yyyy-MM-dd'T'HH:mm:ssZ" |
| `dueDate`    | date    | 否   | 截止日期时间，格式："yyyy-MM-dd'T'HH:mm:ssZ" |
| `timeZone`   | string  | 否   | 时区                                         |
| `reminders`  | list    | 否   | 任务特定的提醒列表                           |
| `repeatFlag` | string  | 否   | 任务重复规则                                 |
| `priority`   | integer | 否   | 任务优先级，默认为"normal"                   |
| `sortOrder`  | integer | 否   | 任务排序                                     |
| `items`      | list    | 否   | 子任务列表                                   |

**响应状态码**:

- `200`: 成功
- `201`: 已创建
- `401`: 未授权
- `403`: 禁止访问
- `404`: 未找到

**请求示例**:

```http
POST /open/v1/task/{{taskId}} HTTP/1.1
Host: api.dida365.com
Content-Type: application/json
Authorization: Bearer {{token}}

{
    "id": "{{taskId}}",
    "projectId": "{{projectId}}",
    "title": "Task Title",
    "priority": 1
}
```

**响应示例**:

```json
{
  "id": "63b7bebb91c0a5474805fcd4",
  "projectId": "6226ff9877acee87727f6bca",
  "title": "Task Title",
  "content": "Task Content",
  "desc": "Task Description",
  "isAllDay": true,
  "startDate": "2019-11-13T03:00:00+0000",
  "dueDate": "2019-11-14T03:00:00+0000",
  "timeZone": "America/Los_Angeles",
  "reminders": ["TRIGGER:P0DT9H0M0S", "TRIGGER:PT0S"],
  "repeatFlag": "RRULE:FREQ=DAILY;INTERVAL=1",
  "priority": 1,
  "status": 0,
  "completedTime": "2019-11-13T03:00:00+0000",
  "sortOrder": 12345,
  "items": [
    {
      "id": "6435074647fd2e6387145f20",
      "status": 1,
      "title": "Item Title",
      "sortOrder": 12345,
      "startDate": "2019-11-13T03:00:00+0000",
      "isAllDay": false,
      "timeZone": "America/Los_Angeles",
      "completedTime": "2019-11-13T03:00:00+0000"
    }
  ]
}
```

### 4. 完成任务

**接口路径**: `POST /open/v1/project/{projectId}/task/{taskId}/complete`

**描述**: 标记任务为完成状态

**路径参数**:

- `projectId` (必需): 项目标识符 (string)
- `taskId` (必需): 任务标识符 (string)

**响应状态码**:

- `200`: 成功
- `201`: 已创建
- `401`: 未授权
- `403`: 禁止访问
- `404`: 未找到

**请求示例**:

```http
POST /open/v1/project/{{projectId}}/task/{{taskId}}/complete HTTP/1.1
Host: api.dida365.com
Authorization: Bearer {{token}}
```

### 5. 删除任务

**接口路径**: `DELETE /open/v1/project/{projectId}/task/{taskId}`

**描述**: 删除指定任务

**路径参数**:

- `projectId` (必需): 项目标识符 (string)
- `taskId` (必需): 任务标识符 (string)

**响应状态码**:

- `200`: 成功
- `201`: 已创建
- `401`: 未授权
- `403`: 禁止访问
- `404`: 未找到

**请求示例**:

```http
DELETE /open/v1/project/{{projectId}}/task/{{taskId}} HTTP/1.1
Host: api.dida365.com
Authorization: Bearer {{token}}
```

## 项目相关接口

### 6. 获取用户项目列表

**接口路径**: `GET /open/v1/project`

**描述**: 获取当前用户的所有项目

**响应状态码**:

- `200`: 成功返回项目列表
- `401`: 未授权
- `403`: 禁止访问
- `404`: 未找到

**请求示例**:

```http
GET /open/v1/project HTTP/1.1
Host: api.dida365.com
Authorization: Bearer {{token}}
```

**响应示例**:

```json
[
  {
    "id": "6226ff9877acee87727f6bca",
    "name": "project name",
    "color": "#F18181",
    "closed": false,
    "groupId": "6436176a47fd2e05f26ef56e",
    "viewMode": "list",
    "permission": "write",
    "kind": "TASK"
  }
]
```

### 7. 根据 ID 获取项目

**接口路径**: `GET /open/v1/project/{projectId}`

**描述**: 根据项目 ID 获取项目详情

**路径参数**:

- `projectId` (必需): 项目标识符 (string)

**响应状态码**:

- `200`: 成功
- `401`: 未授权
- `403`: 禁止访问
- `404`: 未找到

**请求示例**:

```http
GET /open/v1/project/{{projectId}} HTTP/1.1
Host: api.dida365.com
Authorization: Bearer {{token}}
```

**响应示例**:

```json
{
  "id": "6226ff9877acee87727f6bca",
  "name": "project name",
  "color": "#F18181",
  "closed": false,
  "groupId": "6436176a47fd2e05f26ef56e",
  "viewMode": "list",
  "kind": "TASK"
}
```

### 8. 获取项目及其数据

**接口路径**: `GET /open/v1/project/{projectId}/data`

**描述**: 获取项目详情及其包含的任务和列数据

**路径参数**:

- `projectId` (必需): 项目标识符 (string)

**响应状态码**:

- `200`: 成功
- `401`: 未授权
- `403`: 禁止访问
- `404`: 未找到

**请求示例**:

```http
GET /open/v1/project/{{projectId}}/data HTTP/1.1
Host: api.dida365.com
Authorization: Bearer {{token}}
```

**响应示例**:

```json
{
  "project": {
    "id": "6226ff9877acee87727f6bca",
    "name": "project name",
    "color": "#F18181",
    "closed": false,
    "groupId": "6436176a47fd2e05f26ef56e",
    "viewMode": "list",
    "kind": "TASK"
  },
  "tasks": [
    {
      "id": "6247ee29630c800f064fd145",
      "isAllDay": true,
      "projectId": "6226ff9877acee87727f6bca",
      "title": "Task Title",
      "content": "Task Content",
      "desc": "Task Description",
      "timeZone": "America/Los_Angeles",
      "repeatFlag": "RRULE:FREQ=DAILY;INTERVAL=1",
      "startDate": "2019-11-13T03:00:00+0000",
      "dueDate": "2019-11-14T03:00:00+0000",
      "reminders": ["TRIGGER:P0DT9H0M0S", "TRIGGER:PT0S"],
      "priority": 1,
      "status": 0,
      "completedTime": "2019-11-13T03:00:00+0000",
      "sortOrder": 12345,
      "items": [
        {
          "id": "6435074647fd2e6387145f20",
          "status": 0,
          "title": "Subtask Title",
          "sortOrder": 12345,
          "startDate": "2019-11-13T03:00:00+0000",
          "isAllDay": false,
          "timeZone": "America/Los_Angeles",
          "completedTime": "2019-11-13T03:00:00+0000"
        }
      ]
    }
  ],
  "columns": [
    {
      "id": "6226ff9e76e5fc39f2862d1b",
      "projectId": "6226ff9877acee87727f6bca",
      "name": "Column Name",
      "sortOrder": 0
    }
  ]
}
```

### 9. 创建项目

**接口路径**: `POST /open/v1/project`

**描述**: 创建新项目

**请求参数**:

| 参数名      | 类型    | 必需 | 描述                                   |
| ----------- | ------- | ---- | -------------------------------------- |
| `name`      | string  | 是   | 项目名称                               |
| `color`     | string  | 否   | 项目颜色，例如："#F18181"              |
| `sortOrder` | integer | 否   | 项目排序值                             |
| `viewMode`  | string  | 否   | 视图模式："list", "kanban", "timeline" |
| `kind`      | string  | 否   | 项目类型："TASK", "NOTE"               |

**响应状态码**:

- `200`: 成功
- `201`: 已创建
- `401`: 未授权
- `403`: 禁止访问
- `404`: 未找到

**请求示例**:

```http
POST /open/v1/project HTTP/1.1
Host: api.dida365.com
Content-Type: application/json
Authorization: Bearer {{token}}

{
    "name": "project name",
    "color": "#F18181",
    "viewMode": "list",
    "kind": "task"
}
```

**响应示例**:

```json
{
  "id": "6226ff9877acee87727f6bca",
  "name": "project name",
  "color": "#F18181",
  "sortOrder": 0,
  "viewMode": "list",
  "kind": "TASK"
}
```

### 10. 更新项目

**接口路径**: `POST /open/v1/project/{projectId}`

**描述**: 更新现有项目

**路径参数**:

- `projectId` (必需): 项目标识符 (string)

**请求参数**:

| 参数名      | 类型    | 必需 | 描述                                   |
| ----------- | ------- | ---- | -------------------------------------- |
| `name`      | string  | 否   | 项目名称                               |
| `color`     | string  | 否   | 项目颜色                               |
| `sortOrder` | integer | 否   | 排序值，默认为 0                       |
| `viewMode`  | string  | 否   | 视图模式："list", "kanban", "timeline" |
| `kind`      | string  | 否   | 项目类型："TASK", "NOTE"               |

**响应状态码**:

- `200`: 成功
- `201`: 已创建
- `401`: 未授权
- `403`: 禁止访问
- `404`: 未找到

**请求示例**:

```http
POST /open/v1/project/{{projectId}} HTTP/1.1
Host: api.dida365.com
Content-Type: application/json
Authorization: Bearer {{token}}

{
    "name": "Project Name",
    "color": "#F18181",
    "viewMode": "list",
    "kind": "TASK"
}
```

**响应示例**:

```json
{
  "id": "6226ff9877acee87727f6bca",
  "name": "Project Name",
  "color": "#F18181",
  "sortOrder": 0,
  "viewMode": "list",
  "kind": "TASK"
}
```

### 11. 删除项目

**接口路径**: `DELETE /open/v1/project/{projectId}`

**描述**: 删除指定项目

**路径参数**:

- `projectId` (必需): 项目标识符 (string)

**响应状态码**:

- `200`: 成功
- `401`: 未授权
- `403`: 禁止访问
- `404`: 未找到

**请求示例**:

```http
DELETE /open/v1/project/{{projectId}} HTTP/1.1
Host: api.dida365.com
Authorization: Bearer {{token}}
```

## 数据模型定义

### ChecklistItem (子任务)

| 字段名          | 类型    | 描述                                     |
| --------------- | ------- | ---------------------------------------- |
| `id`            | string  | 子任务标识符                             |
| `title`         | string  | 子任务标题                               |
| `status`        | integer | 完成状态 (0: 正常，1: 已完成)            |
| `completedTime` | string  | 完成时间，格式："yyyy-MM-dd'T'HH:mm:ssZ" |
| `isAllDay`      | boolean | 是否全天                                 |
| `sortOrder`     | integer | 排序顺序                                 |
| `startDate`     | string  | 开始时间，格式："yyyy-MM-dd'T'HH:mm:ssZ" |
| `timeZone`      | string  | 时区，例如："America/Los_Angeles"        |

### Task (任务)

| 字段名          | 类型    | 描述                                          |
| --------------- | ------- | --------------------------------------------- |
| `id`            | string  | 任务标识符                                    |
| `projectId`     | string  | 项目 ID                                       |
| `title`         | string  | 任务标题                                      |
| `isAllDay`      | boolean | 是否全天                                      |
| `completedTime` | string  | 完成时间，格式："yyyy-MM-dd'T'HH:mm:ssZ"      |
| `content`       | string  | 任务内容                                      |
| `desc`          | string  | 任务描述                                      |
| `dueDate`       | string  | 截止时间，格式："yyyy-MM-dd'T'HH:mm:ssZ"      |
| `items`         | array   | 子任务列表                                    |
| `priority`      | integer | 优先级 (0: 无，1: 低，3: 中，5: 高)           |
| `reminders`     | array   | 提醒触发器列表                                |
| `repeatFlag`    | string  | 重复规则，例如："RRULE:FREQ=DAILY;INTERVAL=1" |
| `sortOrder`     | integer | 排序顺序                                      |
| `startDate`     | string  | 开始时间，格式："yyyy-MM-dd'T'HH:mm:ssZ"      |
| `status`        | integer | 任务状态 (0: 正常，2: 已完成)                 |
| `timeZone`      | string  | 时区，例如："America/Los_Angeles"             |

### Project (项目)

| 字段名       | 类型    | 描述                                   |
| ------------ | ------- | -------------------------------------- |
| `id`         | string  | 项目标识符                             |
| `name`       | string  | 项目名称                               |
| `color`      | string  | 项目颜色                               |
| `sortOrder`  | integer | 排序值                                 |
| `closed`     | boolean | 项目是否关闭                           |
| `groupId`    | string  | 项目组标识符                           |
| `viewMode`   | string  | 视图模式："list", "kanban", "timeline" |
| `permission` | string  | 权限："read", "write", "comment"       |
| `kind`       | string  | 项目类型："TASK", "NOTE"               |

### Column (列)

| 字段名      | 类型    | 描述       |
| ----------- | ------- | ---------- |
| `id`        | string  | 列标识符   |
| `projectId` | string  | 项目标识符 |
| `name`      | string  | 列名称     |
| `sortOrder` | integer | 排序值     |

### ProjectData (项目数据)

| 字段名    | 类型     | 描述                   |
| --------- | -------- | ---------------------- |
| `project` | Project  | 项目信息               |
| `tasks`   | Task[]   | 项目下的未完成任务列表 |
| `columns` | Column[] | 项目下的列列表         |

## 注意事项

1. **时间格式**: 所有时间字段都使用 ISO 8601 格式："yyyy-MM-dd'T'HH:mm:ssZ"，例如："2019-11-13T03:00:00+0000"
2. **认证**: 所有接口都需要在请求头中包含 Bearer Token
3. **内容类型**: POST 请求需要设置 `Content-Type: application/json`
4. **状态码**:
   - 任务状态：0 = 正常，2 = 已完成
   - 子任务状态：0 = 正常，1 = 已完成
5. **优先级**: 0 = 无，1 = 低，3 = 中，5 = 高
6. **重复规则**: 使用 RRULE 格式，例如："RRULE:FREQ=DAILY;INTERVAL=1"
7. **提醒**: 使用触发器格式，例如：["TRIGGER:P0DT9H0M0S", "TRIGGER:PT0S"]
