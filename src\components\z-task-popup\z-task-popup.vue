<template>
  <u-popup
    border-radius="15"
    :mode="positionType"
    :model-value="show"
    @update:model-value="emits('update:show', $event)"
  >
    <view
      class="p-4 bg-white"
      :style="{
        marginBottom: keyboardHeight + 'px',
        width: sys.isPC ? '600rpx' : '100%',
      }"
    >
      <!-- 快捷输入 -->
      <view v-if="type === 'today'">
        <u-input
          v-model="taskForm.title"
          focus
          trim
          :fixed="true"
          :adjust-position="false"
          show-confirmbar
          type="textarea"
        />
        <view class="flex justify-center items-center justify-between">
          <!-- 编辑区 -->
          <view class="text-20 text-gray">
            <text>添加到：{{ taskDay }}</text>
          </view>
          <view
            @click="onSubmit"
            :class="[taskForm.title ? 'bg-[#64b6f7]' : 'bg-[#ccc]']"
            class="flex bg justify-center items-center rr w-120 h-70"
          >
            <u-icon name="arrow-rightward" color="#fff" size="35"></u-icon>
          </view>
        </view>
      </view>
      <!-- 量化任务 -->
      <view v-if="type === 'kr'">
        <view class="flex">
          <u-input
            trim
            clearable
            :border="true"
            v-model="recVal"
            :adjust-position="false"
            show-confirmbar
            type="number"
          />
          <view
            @click="changeRec(true)"
            class="flex justify-center items-center text-50 bg-[#64b6f7] color-white ml-4 w-80 rr"
            >-</view
          >
          <view
            @click="changeRec(false)"
            class="flex justify-center items-center text-50 bg-[#64b6f7] color-white ml-4 w-80 rr"
            >+</view
          >
        </view>
        <z-button class="mt-4" @click="onSubmit">添加</z-button>
      </view>
    </view>
  </u-popup>
</template>
<script setup>
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  todo: {
    type: Object,
    default: () => ({}),
  },
  // today 今日待办，todo 任务，kr 量化任务
  type: {
    type: String,
    default: 'todo',
  },
  editId: {
    type: String,
    default: '',
  },
  today: {
    type: String,
    default: '',
  },
  parentId: {
    type: String,
    default: '',
  },
  okrId: {
    type: String,
    default: '',
  },
})
const showCalendar = ref(false)
const emits = defineEmits('update:show', 'onSubmit')
const [taskForm, resetTaskForm] = useParams({
  title: '',
  progVal: 0,
  repeatFlag: '',
  date: '',
  krId: '',
  initVal: 0,
  tgtVal: 100,
  valType: 'sum',
  startDate: '',
  endDate: '',
  type: 'todo',
})
const recVal = ref(1)

const sys = useIsPC()

const positionType = computed(() => (sys.isPC ? 'center' : 'bottom'))

// 键盘高度
const keyboardHeight = ref(0)
// 键盘高度回调
const onKeyboardHeight = (res) => {
  if (props.show && positionType.value === 'bottom') keyboardHeight.value = res.height > 50 ? res.height - 50 : 0
}

// TODO 如何处理长按频繁触发函数的问题
// 监听键盘回车
const clickKeyDown = (e) => {
  e.preventDefault()
  // 提交
  if (e.key === 'Enter') {
    onSubmit()
  }
  // 关闭
  if (e.key === 'Escape') {
    emits('update:show', false)
  }
}

watch(
  () => props.show,
  (newValue, oldValue) => {
    if (newValue) {
      // 打开弹窗
      // #ifdef H5
      document.addEventListener('keydown', clickKeyDown) // 监听键盘回车
      // #endif
    } else {
      // 关闭弹窗
      // #ifdef H5
      document.removeEventListener('keydown', clickKeyDown) // 取消监听键盘回车
      // #endif
    }
  }
)

onLoad(() => {
  // #ifdef APP-PLUS
  uni.onKeyboardHeightChange(onKeyboardHeight)
  // #endif
})
onUnload(() => {
  // #ifdef APP-PLUS
  uni.offKeyboardHeightChange(onKeyboardHeight)
  // #endif
  // #ifdef H5
  document.removeEventListener('keydown', clickKeyDown) // 取消监听键盘回车
  // #endif
})

// 增减量化任务进度
const changeRec = (isNeg) => {
  if (isNeg) {
    if (recVal.value === 1) return
    recVal.value -= 1
  } else {
    recVal.value += 1
  }
}

const taskDay = computed(() => {
  if (props.today === dayjs().format('YYYY-MM-DD')) {
    return '今天'
  } else {
    return dayjs(props.today).format('MM-DD')
  }
})

watch(
  () => props.show,
  (newValue, oldValue) => {
    if (newValue) {
      // 打开弹窗
      resetTaskForm({ ...props.todo })
      if (props.type === 'kr') {
        positionType.value = 'center'
      }
    }
  }
)

// 提交
const onSubmit = async () => {
  if (props.type === 'today' && !taskForm.title) return
  if (props.type === 'kr' && recVal.value === '') return
  uni.vibrateShort()
  if (props.today) {
    // 添加到今日待办
    const params = {
      title: taskForm.title,
      startDate: props.today,
      endDate: props.today,
      type: 'todo',
    }
    await addTaskApi(params)
  } else {
    // 量化任务添加记录
    let { recList } = props.todo

    if (!recList) recList = []
    recList.push({
      _id: generateUUID(),
      val: recVal.value,
      recTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      updateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    })
    await updateTaskApi(props.editId, {
      recList,
    })
    uni.showToast({
      title: '添加成功',
      icon: 'none',
    })
  }

  emits('update:show', false)
  emits('onSubmit')
  resetTaskForm()
}

// 关闭弹窗
const closePop = () => {
  emits('update:show', false)
}
</script>
<style scoped lang="scss">
.task-item {
  display: flex;
  align-items: center;
  height: 100rpx;
  .iconfont {
    margin-right: 20rpx;
  }
}
</style>
