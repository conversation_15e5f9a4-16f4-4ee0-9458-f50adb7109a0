// 工具函数模块
// 包含通用的工具函数和辅助方法

// 生成UUID
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0
    const v = c == 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

// 提取项目关键词
function extractProjectKeyword(input) {
  const matches = input.match(/(\w+)项目|(\w+)project/gi)
  if (matches && matches.length > 0) {
    return matches[0].replace(/项目|project/gi, '')
  }
  return ''
}

// 延迟函数
function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

// 深度克隆对象
function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime())
  }

  if (obj instanceof Array) {
    return obj.map((item) => deepClone(item))
  }

  if (typeof obj === 'object') {
    const cloned = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key])
      }
    }
    return cloned
  }

  return obj
}

// 格式化时间戳
function formatTimestamp(timestamp) {
  const date = new Date(timestamp)
  return date.toISOString().replace('T', ' ').substring(0, 19)
}

// 计算数组平均值
function calculateAverage(array) {
  if (!Array.isArray(array) || array.length === 0) {
    return 0
  }
  return array.reduce((sum, val) => sum + val, 0) / array.length
}

// 安全的JSON解析
function safeJsonParse(jsonString, defaultValue = null) {
  try {
    return JSON.parse(jsonString)
  } catch (error) {
    console.warn('JSON解析失败:', error.message)
    return defaultValue
  }
}

// 检查对象是否为空
function isEmpty(obj) {
  if (obj === null || obj === undefined) {
    return true
  }

  if (typeof obj === 'string' || Array.isArray(obj)) {
    return obj.length === 0
  }

  if (typeof obj === 'object') {
    return Object.keys(obj).length === 0
  }

  return false
}

// 去除字符串首尾空格并处理null/undefined
function safeTrim(str) {
  if (typeof str !== 'string') {
    return ''
  }
  return str.trim()
}

module.exports = {
  generateUUID,
  extractProjectKeyword,
  delay,
  deepClone,
  formatTimestamp,
  calculateAverage,
  safeJsonParse,
  isEmpty,
  safeTrim,
}
