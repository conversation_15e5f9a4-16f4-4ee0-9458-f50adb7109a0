---
description: 
globs: 
alwaysApply: false
---
# 调试复杂条件的最佳实践

## 添加详细日志

当调试复杂的条件逻辑时，应该将复杂表达式拆分成独立变量，并为每个部分添加详细日志。这有助于理解复杂条件的计算过程。

### 示例 - 不良实践:

```js
const _disabled = (!dayjs(props.ticketInfo._day).isAfter(dayjs()) && (_item?.timeShareEndTime && compareTimes(dayjs().format('HH'), _item?.timeShareEndTime) === 1) || !_item?.stockAmount);
```

### 示例 - 良好实践:

```js
// 将复杂条件拆分为独立变量
const isToday = !dayjs(props.ticketInfo._day).isAfter(dayjs());
const isTimeEnded = _item?.timeShareEndTime && compareTimes(dayjs().format('HH'), _item?.timeShareEndTime) === 1;
const hasNoStock = !_item?.stockAmount;

// 为每个部分添加详细日志
console.log('时段信息:', _item);
console.log('是今天?', isToday);
console.log('时段结束时间:', _item?.timeShareEndTime);
console.log('当前时间超过结束时间?', isTimeEnded);
console.log('库存量:', _item?.stockAmount);
console.log('无库存?', hasNoStock);

// 最终计算结果及日志
const _disabled = (isToday && isTimeEnded) || hasNoStock;
console.log('最终禁用状态:', _disabled);
```

## 调试对象和数组

调试对象和数组时，应提供上下文信息，并使用有意义的标签:

```js
// 提供上下文的日志
console.log('_timeList:', _timeList);
console.log('当前选择日期:', props.ticketInfo._day);
console.log('当前系统时间:', dayjs().format('YYYY-MM-DD HH:mm:ss'));
```

## 重要日志原则

1. 使用有意义的标签，避免单纯输出变量
2. 将复杂条件拆分为单个逻辑组件
3. 记录中间计算步骤和最终结果
4. 使用中文标签可以更快速地识别日志内容
5. 上线前记得移除或注释掉开发调试日志

