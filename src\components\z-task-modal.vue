<template>
  <div id="add-task-modal" class="modal" :class="{ show: modelValue }" @click.self="closeModal">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title">{{ title || (isEdit ? '编辑关联任务' : '添加关联任务') }}</h3>
        <div class="modal-close" @click="closeModal">&times;</div>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <div class="form-label">任务名称</div>
          <input type="text" class="form-input" placeholder="请输入任务名称" v-model="taskData.title" />
        </div>
        <div class="form-group" v-if="showKrInfo">
          <div class="form-label">关联的关键结果</div>
          <div class="related-kr">
            <div class="kr-info">
              <div class="kr-name">{{ krInfo.name }}</div>
              <div class="kr-progress">当前进度：{{ krInfo.progress }}</div>
            </div>
          </div>
        </div>
        <div class="form-group">
          <div class="form-label">任务循环</div>
          <div class="cycle-options">
            <div class="cycle-btn" :class="{ active: taskData.cycle === 'none' }" @click="handleCycleChange('none')">
              不循环
            </div>
            <div class="cycle-btn" :class="{ active: taskData.cycle === 'daily' }" @click="handleCycleChange('daily')">
              每天
            </div>
            <div
              class="cycle-btn"
              :class="{ active: taskData.cycle === 'weekly' }"
              @click="handleCycleChange('weekly')"
            >
              每周
            </div>
            <div
              class="cycle-btn"
              :class="{ active: taskData.cycle === 'monthly' }"
              @click="handleCycleChange('monthly')"
            >
              每月
            </div>
          </div>
        </div>
        <div class="form-group">
          <div class="form-label">{{ showDateRange ? '任务日期区间' : '任务日期' }}</div>
          <div v-if="!showDateRange" class="date-input">
            <input type="date" class="form-input" v-model="taskData.date" />
          </div>
          <div v-else class="date-range-container">
            <div class="date-range-item">
              <div class="date-label">开始日期</div>
              <input type="date" class="form-input" v-model="taskData.startDate" />
            </div>
            <div class="date-range-item">
              <div class="date-label">结束日期</div>
              <input type="date" class="form-input" v-model="taskData.endDate" />
            </div>
          </div>
          <div class="date-range-toggle" @click="toggleDateRange" v-if="taskData.cycle === 'none'">
            <span>{{ showDateRange ? '切换为单日' : '切换为日期区间' }}</span>
          </div>
        </div>
        <div class="form-group" v-if="showProgress">
          <div class="form-label">
            完成后贡献的进度值
            <span v-if="isTaskCompleted" class="status-badge">已完成</span>
          </div>
          <div class="progress-input-container">
            <input
              type="number"
              class="form-input"
              min="0"
              max="100"
              step="1"
              v-model.number="taskData.progressValue"
              :disabled="isProgressDisabled"
              :class="{ disabled: isProgressDisabled }"
            />
            <span class="progress-unit">%</span>
          </div>
          <div v-if="isTaskCompleted" class="info-message">
            <i class="fas fa-info-circle"></i> 已完成的任务不能修改进度值
          </div>
        </div>
        <div v-if="errorMsg" class="error-message">{{ errorMsg }}</div>
      </div>
      <div class="modal-footer">
        <div v-if="isEdit && taskId" class="btn-delete" @click="handleDelete">删除</div>
        <div style="flex: 1"></div>
        <div class="btn-cancel" @click="closeModal">取消</div>
        <div class="btn-save" @click="handleSave" :class="{ 'btn-disabled': isSaving }">
          {{ isSaving ? '保存中...' : '保存' }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, defineProps, defineEmits, watch, computed } from 'vue'
import { addTaskApi, updateTaskApi, delTaskApi } from '@/api/task'

interface TaskData {
  title: string
  date: string
  startDate: string
  endDate: string
  cycle: 'none' | 'daily' | 'weekly' | 'monthly'
  progressValue: number
}

interface KrInfo {
  name: string
  progress: string
  _id?: string
  okrId?: string
}

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  title: {
    type: String,
    default: '',
  },
  showKrInfo: {
    type: Boolean,
    default: true,
  },
  showProgress: {
    type: Boolean,
    default: true,
  },
  krInfo: {
    type: Object as () => KrInfo,
    default: () => ({
      name: '',
      progress: '',
      _id: '',
      okrId: '',
    }),
  },
  initialData: {
    type: Object as () => Partial<TaskData>,
    default: () => ({}),
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
  taskId: {
    type: String,
    default: '',
  },
  // 任务状态 0: 未完成 1: 已完成 2: 放弃
  status: {
    type: Number,
    default: 0,
  },
})

const emit = defineEmits(['update:modelValue', 'save', 'save-success', 'delete-success'])

const today = new Date().toISOString().split('T')[0]

const taskData = reactive<TaskData>({
  title: '',
  date: today,
  startDate: today,
  endDate: today,
  cycle: 'none',
  progressValue: 5,
})

const isSaving = ref(false)
const errorMsg = ref('')
const showDateRange = ref(false)

// 计算属性：判断是否是已完成任务
const isTaskCompleted = computed(() => props.status === 1)

// 计算属性：是否禁用进度值输入
const isProgressDisabled = computed(() => isTaskCompleted.value)

watch(
  () => props.initialData,
  (newVal) => {
    if (newVal) {
      // 处理初始数据
      const initialData = { ...newVal }

      // 如果有 startDate 和 endDate，判断是否应该显示日期区间
      if (initialData.startDate && initialData.endDate && initialData.startDate !== initialData.endDate) {
        showDateRange.value = true
      } else if (initialData.cycle && initialData.cycle !== 'none') {
        showDateRange.value = true
      }

      // 如果没有 startDate 或 endDate，但有 date，则使用 date 值
      if ((!initialData.startDate || !initialData.endDate) && initialData.date) {
        initialData.startDate = initialData.date
        initialData.endDate = initialData.date
      }

      Object.assign(taskData, {
        ...taskData,
        ...initialData,
      })
    }
  },
  { deep: true, immediate: true }
)

// 监听循环模式变化
watch(
  () => taskData.cycle,
  (newVal) => {
    if (newVal !== 'none' && !showDateRange.value) {
      showDateRange.value = true
    }
  }
)

const closeModal = () => {
  emit('update:modelValue', false)
}

const handleCycleChange = (cycle: 'none' | 'daily' | 'weekly' | 'monthly') => {
  taskData.cycle = cycle
  if (cycle !== 'none') {
    showDateRange.value = true
  }
}

const toggleDateRange = () => {
  showDateRange.value = !showDateRange.value

  // 切换时同步日期
  if (showDateRange.value) {
    taskData.startDate = taskData.date
    taskData.endDate = taskData.date
  } else {
    taskData.date = taskData.startDate
  }
}

const handleDelete = () => {
  if (!props.taskId) {
    uni.showToast({
      title: '任务 ID 不存在',
      icon: 'none',
    })
    return
  }

  uni.showModal({
    title: '确认删除',
    content: '确定要删除此任务吗？该操作不可恢复。',
    confirmColor: '#FF0000',
    success: async (res) => {
      if (res.confirm) {
        await deleteTask()
      }
    },
  })
}

const deleteTask = async () => {
  try {
    uni.showLoading({
      title: '正在删除...',
    })

    await delTaskApi(props.taskId)

    uni.hideLoading()

    uni.showToast({
      title: '删除成功',
      icon: 'success',
    })

    emit('delete-success', { _id: props.taskId })

    closeModal()
  } catch (error) {
    console.error('删除任务失败：', error)
    uni.hideLoading()
    uni.showToast({
      title: '删除失败',
      icon: 'none',
    })
  }
}

const handleSave = async () => {
  if (!taskData.title.trim()) {
    errorMsg.value = '请输入任务名称'
    uni.showToast({
      title: '请输入任务名称',
      icon: 'none',
    })
    return
  }

  if (showDateRange.value && new Date(taskData.endDate) < new Date(taskData.startDate)) {
    errorMsg.value = '结束日期不能早于开始日期'
    uni.showToast({
      title: '结束日期不能早于开始日期',
      icon: 'none',
    })
    return
  }

  isSaving.value = true
  errorMsg.value = ''

  try {
    if (props.isEdit && props.taskId) {
      const updateData = {
        title: taskData.title,
        startDate: showDateRange.value ? taskData.startDate : taskData.date,
        endDate: showDateRange.value ? taskData.endDate : taskData.date,
        repeatFlag: taskData.cycle !== 'none' ? `FREQ=${taskData.cycle.toUpperCase()}` : '',
        progVal: taskData.progressValue || 0,
      }

      await updateTaskApi(props.taskId, updateData)

      uni.showToast({
        title: '更新任务成功',
        icon: 'success',
      })

      emit('save-success', {
        type: 'update',
        data: { _id: props.taskId, ...updateData },
      })
    } else {
      const newTaskData = {
        title: taskData.title,
        content: '',
        type: 'todo' as const,
        okrId: props.krInfo.okrId || '',
        parentId: props.krInfo._id || '',
        startDate: showDateRange.value ? taskData.startDate : taskData.date,
        endDate: showDateRange.value ? taskData.endDate : taskData.date,
        status: 0 as const,
        repeatFlag: taskData.cycle !== 'none' ? `FREQ=${taskData.cycle.toUpperCase()}` : '',
        progVal: taskData.progressValue || 0,
      }

      const taskId = await addTaskApi(newTaskData)

      if (taskId) {
        uni.showToast({
          title: '添加任务成功',
          icon: 'success',
        })

        emit('save-success', {
          type: 'add',
          data: { _id: taskId, ...newTaskData },
        })
      }
    }

    emit('save', { ...taskData })
    closeModal()
  } catch (error) {
    console.error(props.isEdit ? '更新任务失败：' : '添加任务失败：', error)
    errorMsg.value = props.isEdit ? '更新任务失败' : '添加任务失败'
    uni.showToast({
      title: errorMsg.value,
      icon: 'none',
    })
  } finally {
    isSaving.value = false
  }
}
</script>

<style scoped lang="scss">
/* Modal styles */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 100;
  justify-content: center;
  align-items: center;

  &.show {
    display: flex;
  }
}

.modal-content {
  background: var(--color-white);
  border-radius: 16px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--color-gray-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-gray-800);
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: var(--color-gray-500);
  cursor: pointer;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 16px 20px;
  border-top: 1px solid var(--color-gray-200);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-gray-700);
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--color-gray-300);
  border-radius: 8px;
  font-size: 14px;
  color: var(--color-gray-800);
  background: var(--color-white);
  box-sizing: border-box;

  &:focus {
    border-color: var(--color-primary);
    outline: none;
    box-shadow: 0 0 0 2px var(--color-primary-light);
  }

  &:disabled,
  &.disabled {
    background-color: var(--color-gray-100);
    color: var(--color-gray-500);
    border-color: var(--color-gray-200);
    cursor: not-allowed;
  }
}

.related-kr {
  padding: 12px;
  background: var(--color-gray-50);
  border-radius: 8px;
  border: 1px solid var(--color-gray-200);
}

.kr-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.kr-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-gray-800);
}

.kr-progress {
  font-size: 12px;
  color: var(--color-gray-600);
}

.cycle-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.cycle-btn {
  padding: 8px 16px;
  border-radius: 6px;
  background: var(--color-gray-50);
  border: 1px solid var(--color-gray-200);
  color: var(--color-gray-700);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  text-align: center;
  user-select: none;

  &:hover {
    background: var(--color-gray-100);
  }

  &.active {
    background: var(--color-primary);
    border-color: var(--color-primary);
    color: var(--color-white);
  }
}

.progress-input-container {
  display: flex;
  align-items: center;
}

.progress-unit {
  margin-left: 8px;
  font-size: 14px;
  color: var(--color-gray-600);
}

.btn-cancel {
  padding: 10px 16px;
  border: 1px solid var(--color-gray-300);
  background: var(--color-white);
  color: var(--color-gray-700);
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: var(--color-gray-50);
  }
}

.btn-save {
  padding: 10px 16px;
  border: none;
  background: var(--color-primary);
  color: var(--color-white);
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: var(--color-primary-dark);
  }
}

.btn-delete {
  padding: 10px 16px;
  border: 1px solid var(--color-danger, #dc3545);
  background: var(--color-white);
  color: var(--color-danger, #dc3545);
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: rgba(220, 53, 69, 0.1);
  }
}

.cursor-pointer {
  cursor: pointer;
}

.error-message {
  color: #f44336;
  font-size: 14px;
  margin-top: 12px;
  padding: 8px;
  background-color: rgba(244, 67, 54, 0.1);
  border-radius: 4px;
}

.btn-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.status-badge {
  background-color: #4caf50;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 8px;
  font-size: 12px;
  font-weight: normal;
}

.info-message {
  color: #666;
  font-size: 12px;
  margin-top: 8px;
  padding: 8px;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 4px;
  border: 1px solid #ddd;
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 日期区间相关样式 */
.date-range-container {
  display: flex;
  gap: 12px;
  width: 100%;
}

.date-range-item {
  flex: 1;
}

.date-label {
  font-size: 12px;
  color: var(--color-gray-600);
  margin-bottom: 4px;
}

.date-range-toggle {
  margin-top: 8px;
  font-size: 13px;
  color: var(--color-primary);
  cursor: pointer;
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;

  &:hover {
    background-color: var(--color-primary-light, rgba(0, 123, 255, 0.1));
  }
}
</style>
