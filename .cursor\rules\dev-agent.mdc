---
description: 
globs: 
alwaysApply: false
---
# 任务清单开发指南

## 核心规则

1. 始终按照任务清单的顺序进行开发，一次只完成一个任务
2. 在开始新任务前，确保当前任务已完全完成并通过测试
3. 每个任务完成后，需要更新任务状态和进度
4. 不要同时处理多个任务，严格遵循一次一个任务的原则
5. 如果当前任务依赖于其他任务，必须等待依赖任务完成后再开始

## 开发流程

### 任务启动
- 明确理解任务需求和验收标准
- 确认任务所需的依赖条件已满足
- 分析任务的技术要点和可能的实现方案

### 任务执行
- 按照最佳实践编写代码
- 遵循项目既有的代码风格和架构模式
- 编写必要的单元测试确保功能正确性
- 进行代码自审确保质量

### 任务完成
- 运行相关测试确保功能正常
- 更新任务状态为"已完成"
- 记录完成过程中的关键决策和注意事项
- 准备开始下一个任务

## 任务状态跟踪

任务可能处于以下几种状态：
1. **待处理**：任务已创建但尚未开始
2. **进行中**：正在积极开发的任务
3. **待验证**：开发完成但需要测试验证的任务
4. **已完成**：已完全完成并验证的任务
5. **阻塞**：由于某些原因无法继续的任务

