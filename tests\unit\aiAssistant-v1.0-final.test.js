/**
 * AI助手V1.0工具注册系统最终测试
 * 测试工具注册表、参数验证器和动态提示词生成器功能
 */

import { describe, it, expect } from '@jest/globals'

// 模拟工具注册表和相关类（从实际代码中复制）
const TOOL_REGISTRY = {
  getProjects: {
    name: 'getProjects',
    description: '获取滴答清单中的所有项目',
    usage: '当用户想要查看、搜索项目时使用',
    parameters: {
      filter: {
        type: 'string',
        required: false,
        default: '',
        description: '项目名称筛选关键词',
        validation: {
          maxLength: 50,
          pattern: '^[\\u4e00-\\u9fa5a-zA-Z0-9\\s]*$'
        }
      }
    },
    metadata: {
      priority: 0.8,
      category: 'data_retrieval',
      estimatedTime: 1500,
      retryable: true
    },
    cloudFunction: 'dida-todo',
    method: 'getProjects'
  },
  
  getTasks: {
    name: 'getTasks',
    description: '获取指定项目下的任务列表',
    usage: '当用户想要查看、搜索、查询任务时使用',
    parameters: {
      projectId: {
        type: 'string',
        required: true,
        description: '项目ID，必须是有效的项目标识符',
        validation: {
          pattern: '^[a-zA-Z0-9-_]+$',
          minLength: 1
        }
      },
      completed: {
        type: 'boolean',
        required: false,
        default: false,
        description: '是否获取已完成的任务'
      },
      limit: {
        type: 'number',
        required: false,
        default: 50,
        description: '返回任务数量限制',
        validation: {
          minimum: 1,
          maximum: 100
        }
      }
    },
    metadata: {
      priority: 0.9,
      category: 'data_retrieval',
      estimatedTime: 2000,
      retryable: true
    },
    cloudFunction: 'dida-todo',
    method: 'getTasks'
  }
}

class ParameterValidator {
  static validate(toolName, parameters) {
    const tool = TOOL_REGISTRY[toolName]
    if (!tool) {
      throw new Error(`未找到工具：${toolName}`)
    }

    const validatedParams = {}
    const errors = []

    for (const [paramName, paramConfig] of Object.entries(tool.parameters)) {
      const value = parameters[paramName]
      
      // 检查必需参数
      if (paramConfig.required && (value === undefined || value === null)) {
        errors.push(`缺少必需参数：${paramName}`)
        continue
      }

      // 应用默认值
      const finalValue = value !== undefined ? value : paramConfig.default

      if (finalValue !== undefined) {
        // 类型验证
        const typeError = this.validateType(paramName, finalValue, paramConfig.type)
        if (typeError) {
          errors.push(typeError)
          continue
        }

        // 验证规则检查
        if (paramConfig.validation) {
          const validationError = this.validateRules(paramName, finalValue, paramConfig.validation)
          if (validationError) {
            errors.push(validationError)
            continue
          }
        }

        validatedParams[paramName] = finalValue
      }
    }

    if (errors.length > 0) {
      throw new Error(`参数验证失败：${errors.join(', ')}`)
    }

    return validatedParams
  }

  static validateType(paramName, value, expectedType) {
    const actualType = typeof value
    
    switch (expectedType) {
      case 'string':
        if (actualType !== 'string') {
          return `参数 ${paramName} 应为字符串类型，实际为 ${actualType}`
        }
        break
      case 'number':
        if (actualType !== 'number' || isNaN(value)) {
          return `参数 ${paramName} 应为数字类型，实际为 ${actualType}`
        }
        break
      case 'boolean':
        if (actualType !== 'boolean') {
          return `参数 ${paramName} 应为布尔类型，实际为 ${actualType}`
        }
        break
    }
    
    return null
  }

  static validateRules(paramName, value, validation) {
    // 数值范围验证
    if (typeof value === 'number') {
      if (validation.minimum !== undefined && value < validation.minimum) {
        return `参数 ${paramName} 不能小于 ${validation.minimum}`
      }
      if (validation.maximum !== undefined && value > validation.maximum) {
        return `参数 ${paramName} 不能大于 ${validation.maximum}`
      }
    }

    // 字符串验证
    if (typeof value === 'string') {
      if (validation.minLength !== undefined && value.length < validation.minLength) {
        return `参数 ${paramName} 长度不能小于 ${validation.minLength}`
      }
      if (validation.maxLength !== undefined && value.length > validation.maxLength) {
        return `参数 ${paramName} 长度不能大于 ${validation.maxLength}`
      }
      if (validation.pattern && !new RegExp(validation.pattern).test(value)) {
        return `参数 ${paramName} 格式不符合要求：${validation.pattern}`
      }
    }

    return null
  }
}

function generateToolPrompt(toolRegistry) {
  let toolPrompt = '你可以使用以下工具来帮助用户完成任务：\n\n'

  for (const [toolKey, tool] of Object.entries(toolRegistry)) {
    toolPrompt += `**${tool.name}** (${toolKey})\n`
    toolPrompt += `- 功能：${tool.description}\n`
    toolPrompt += `- 使用场景：${tool.usage}\n`
    toolPrompt += `- 优先级：${tool.metadata?.priority || 'N/A'}\n`
    toolPrompt += `- 参数：\n`

    if (tool.parameters) {
      for (const [paramName, param] of Object.entries(tool.parameters)) {
        const required = param.required ? '必需' : '可选'
        const defaultValue = param.default ? ` (默认: ${param.default})` : ''
        toolPrompt += `  - ${paramName} (${param.type}, ${required}${defaultValue}): ${param.description}\n`
      }
    }
    
    toolPrompt += '\n'
  }

  return toolPrompt
}

describe('AI助手V1.0工具注册系统最终测试', () => {
  describe('工具注册表结构验证', () => {
    it('应该包含getProjects和getTasks工具', () => {
      expect(TOOL_REGISTRY.getProjects).toBeDefined()
      expect(TOOL_REGISTRY.getTasks).toBeDefined()
      expect(TOOL_REGISTRY.getProjects.name).toBe('getProjects')
      expect(TOOL_REGISTRY.getTasks.name).toBe('getTasks')
    })

    it('工具应该包含完整的元数据', () => {
      const tool = TOOL_REGISTRY.getProjects
      expect(tool.metadata.priority).toBe(0.8)
      expect(tool.metadata.category).toBe('data_retrieval')
    })
  })

  describe('参数验证器功能', () => {
    it('应该验证正确的参数', () => {
      const validParams = { projectId: 'test-123', completed: false, limit: 25 }
      const result = ParameterValidator.validate('getTasks', validParams)
      expect(result.projectId).toBe('test-123')
      expect(result.completed).toBe(false)
      expect(result.limit).toBe(25)
    })

    it('应该检测缺少必需参数', () => {
      const invalidParams = { completed: false }
      expect(() => ParameterValidator.validate('getTasks', invalidParams))
        .toThrow('缺少必需参数：projectId')
    })

    it('应该应用默认值', () => {
      const params = { projectId: 'test-123' }
      const result = ParameterValidator.validate('getTasks', params)
      expect(result.completed).toBe(false)
      expect(result.limit).toBe(50)
    })
  })

  describe('动态提示词生成器', () => {
    it('应该生成包含工具信息的提示词', () => {
      const prompt = generateToolPrompt(TOOL_REGISTRY)
      expect(prompt).toContain('getProjects')
      expect(prompt).toContain('getTasks')
      expect(prompt).toContain('功能：')
      expect(prompt).toContain('参数：')
    })

    it('应该包含工具的详细信息', () => {
      const prompt = generateToolPrompt(TOOL_REGISTRY)
      expect(prompt).toContain('获取滴答清单中的所有项目')
      expect(prompt).toContain('获取指定项目下的任务列表')
      expect(prompt).toContain('优先级：0.8')
      expect(prompt).toContain('优先级：0.9')
    })
  })

  describe('V1.0版本功能验证', () => {
    it('应该生成符合V1.0版本要求的系统提示词', () => {
      const toolPrompt = generateToolPrompt(TOOL_REGISTRY)
      const systemPrompt = `你是一个专业的任务分析助手。你的主要职责是理解用户输入的内容，并判断用户的意图类型。

${toolPrompt}

请分析用户输入内容，并将其归类为以下三种意图之一：
1. create_task: 当用户想要创建、添加、设置、安排一个新任务时
2. find_task: 当用户想要查询、搜索、查看已有的任务时
3. chat: 其他所有不属于创建任务或查找任务的内容，视为一般闲聊

注意：
- 虽然现在有可用的工具，但在V1.0版本中暂不执行工具调用，仅进行意图识别。`
      
      expect(systemPrompt).toContain('getProjects')
      expect(systemPrompt).toContain('getTasks')
      expect(systemPrompt).toContain('虽然现在有可用的工具，但在V1.0版本中暂不执行工具调用，仅进行意图识别')
    })
  })
})
