function e(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function t(e,t){for(var r=0;t.length>r;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function r(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function n(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);t>r;r++)n[r]=e[r];return n}function i(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return n(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var i=0,o=function(){};return{s:o,n:function(){return e.length>i?{done:!1,value:e[i++]}:{done:!0}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,u=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return s=e.done,e},e:function(e){u=!0,a=e},f:function(){try{s||null==r.return||r.return()}finally{if(u)throw a}}}}
/**

 */
var o;!function(t){var n=function(){function n(t,r,i,o){if(e(this,n),this.version=void 0,this.errorCorrectionLevel=void 0,this.size=void 0,this.mask=void 0,this.modules=[],this.isFunction=[],this.version=t,this.errorCorrectionLevel=r,n.MIN_VERSION>t||t>n.MAX_VERSION)throw new RangeError("Version value out of range");if(-1>o||o>7)throw new RangeError("Mask value out of range");this.size=4*t+17;for(var a=[],u=0;this.size>u;u++)a.push(!1);for(var h=0;this.size>h;h++)this.modules.push(a.slice()),this.isFunction.push(a.slice());this.drawFunctionPatterns();var l=this.addEccAndInterleave(i);if(this.drawCodewords(l),-1==o)for(var f=1e9,c=0;8>c;c++){this.applyMask(c),this.drawFormatBits(c);var d=this.getPenaltyScore();f>d&&(o=c,f=d),this.applyMask(c)}s(o>=0&&7>=o),this.mask=o,this.applyMask(o),this.drawFormatBits(o),this.isFunction=[]}return r(n,[{key:"getModule",value:function(e,t){return e>=0&&this.size>e&&t>=0&&this.size>t&&this.modules[t][e]}},{key:"getModules",value:function(){return this.modules}},{key:"drawFunctionPatterns",value:function(){for(var e=0;this.size>e;e++)this.setFunctionModule(6,e,e%2==0),this.setFunctionModule(e,6,e%2==0);this.drawFinderPattern(3,3),this.drawFinderPattern(this.size-4,3),this.drawFinderPattern(3,this.size-4);for(var t=this.getAlignmentPatternPositions(),r=t.length,n=0;r>n;n++)for(var i=0;r>i;i++)0==n&&0==i||0==n&&i==r-1||n==r-1&&0==i||this.drawAlignmentPattern(t[n],t[i]);this.drawFormatBits(0),this.drawVersion()}},{key:"drawFormatBits",value:function(e){for(var t=this.errorCorrectionLevel.formatBits<<3|e,r=t,n=0;10>n;n++)r=r<<1^1335*(r>>>9);var i=21522^(t<<10|r);s(i>>>15==0);for(var o=0;5>=o;o++)this.setFunctionModule(8,o,a(i,o));this.setFunctionModule(8,7,a(i,6)),this.setFunctionModule(8,8,a(i,7)),this.setFunctionModule(7,8,a(i,8));for(var u=9;15>u;u++)this.setFunctionModule(14-u,8,a(i,u));for(var h=0;8>h;h++)this.setFunctionModule(this.size-1-h,8,a(i,h));for(var l=8;15>l;l++)this.setFunctionModule(8,this.size-15+l,a(i,l));this.setFunctionModule(8,this.size-8,!0)}},{key:"drawVersion",value:function(){if(this.version>=7){for(var e=this.version,t=0;12>t;t++)e=e<<1^7973*(e>>>11);var r=this.version<<12|e;s(r>>>18==0);for(var n=0;18>n;n++){var i=a(r,n),o=this.size-11+n%3,u=Math.floor(n/3);this.setFunctionModule(o,u,i),this.setFunctionModule(u,o,i)}}}},{key:"drawFinderPattern",value:function(e,t){for(var r=-4;4>=r;r++)for(var n=-4;4>=n;n++){var i=Math.max(Math.abs(n),Math.abs(r)),o=e+n,a=t+r;o>=0&&this.size>o&&a>=0&&this.size>a&&this.setFunctionModule(o,a,2!=i&&4!=i)}}},{key:"drawAlignmentPattern",value:function(e,t){for(var r=-2;2>=r;r++)for(var n=-2;2>=n;n++)this.setFunctionModule(e+n,t+r,1!=Math.max(Math.abs(n),Math.abs(r)))}},{key:"setFunctionModule",value:function(e,t,r){this.modules[t][e]=r,this.isFunction[t][e]=!0}},{key:"addEccAndInterleave",value:function(e){var t=this.version,r=this.errorCorrectionLevel;if(e.length!=n.getNumDataCodewords(t,r))throw new RangeError("Invalid argument");for(var i=n.NUM_ERROR_CORRECTION_BLOCKS[r.ordinal][t],o=n.ECC_CODEWORDS_PER_BLOCK[r.ordinal][t],a=Math.floor(n.getNumRawDataModules(t)/8),u=i-a%i,h=Math.floor(a/i),l=[],f=n.reedSolomonComputeDivisor(o),c=0,d=0;i>c;c++){var v=e.slice(d,d+h-o+(u>c?0:1));d+=v.length;var m=n.reedSolomonComputeRemainder(v,f);u>c&&v.push(0),l.push(v.concat(m))}for(var g=[],y=function(e){l.forEach((function(t,r){e==h-o&&u>r||g.push(t[e])}))},E=0;l[0].length>E;E++)y(E);return s(g.length==a),g}},{key:"drawCodewords",value:function(e){if(e.length!=Math.floor(n.getNumRawDataModules(this.version)/8))throw new RangeError("Invalid argument");for(var t=0,r=this.size-1;r>=1;r-=2){6==r&&(r=5);for(var i=0;this.size>i;i++)for(var o=0;2>o;o++){var u=r-o,h=0==(r+1&2)?this.size-1-i:i;!this.isFunction[h][u]&&8*e.length>t&&(this.modules[h][u]=a(e[t>>>3],7-(7&t)),t++)}}s(t==8*e.length)}},{key:"applyMask",value:function(e){if(0>e||e>7)throw new RangeError("Mask value out of range");for(var t=0;this.size>t;t++)for(var r=0;this.size>r;r++){var n=void 0;switch(e){case 0:n=(r+t)%2==0;break;case 1:n=t%2==0;break;case 2:n=r%3==0;break;case 3:n=(r+t)%3==0;break;case 4:n=(Math.floor(r/3)+Math.floor(t/2))%2==0;break;case 5:n=r*t%2+r*t%3==0;break;case 6:n=(r*t%2+r*t%3)%2==0;break;case 7:n=((r+t)%2+r*t%3)%2==0;break;default:throw Error("Unreachable")}!this.isFunction[t][r]&&n&&(this.modules[t][r]=!this.modules[t][r])}}},{key:"getPenaltyScore",value:function(){for(var e=0,t=0;this.size>t;t++){for(var r=!1,o=0,a=[0,0,0,0,0,0,0],u=0;this.size>u;u++)this.modules[t][u]==r?5==++o?e+=n.PENALTY_N1:o>5&&e++:(this.finderPenaltyAddHistory(o,a),r||(e+=this.finderPenaltyCountPatterns(a)*n.PENALTY_N3),r=this.modules[t][u],o=1);e+=this.finderPenaltyTerminateAndCount(r,o,a)*n.PENALTY_N3}for(var h=0;this.size>h;h++){for(var l=!1,f=0,c=[0,0,0,0,0,0,0],d=0;this.size>d;d++)this.modules[d][h]==l?5==++f?e+=n.PENALTY_N1:f>5&&e++:(this.finderPenaltyAddHistory(f,c),l||(e+=this.finderPenaltyCountPatterns(c)*n.PENALTY_N3),l=this.modules[d][h],f=1);e+=this.finderPenaltyTerminateAndCount(l,f,c)*n.PENALTY_N3}for(var v=0;this.size-1>v;v++)for(var m=0;this.size-1>m;m++){var g=this.modules[v][m];g==this.modules[v][m+1]&&g==this.modules[v+1][m]&&g==this.modules[v+1][m+1]&&(e+=n.PENALTY_N2)}var y,E=0,C=i(this.modules);try{for(C.s();!(y=C.n()).done;){E=y.value.reduce((function(e,t){return e+(t?1:0)}),E)}}catch(e){C.e(e)}finally{C.f()}var M=this.size*this.size,w=Math.ceil(Math.abs(20*E-10*M)/M)-1;return s(w>=0&&9>=w),s((e+=w*n.PENALTY_N4)>=0&&2568888>=e),e}},{key:"getAlignmentPatternPositions",value:function(){if(1==this.version)return[];for(var e=Math.floor(this.version/7)+2,t=32==this.version?26:2*Math.ceil((4*this.version+4)/(2*e-2)),r=[6],n=this.size-7;e>r.length;n-=t)r.splice(1,0,n);return r}},{key:"finderPenaltyCountPatterns",value:function(e){var t=e[1];s(3*this.size>=t);var r=t>0&&e[2]==t&&e[3]==3*t&&e[4]==t&&e[5]==t;return(!r||4*t>e[0]||t>e[6]?0:1)+(!r||4*t>e[6]||t>e[0]?0:1)}},{key:"finderPenaltyTerminateAndCount",value:function(e,t,r){return e&&(this.finderPenaltyAddHistory(t,r),t=0),this.finderPenaltyAddHistory(t+=this.size,r),this.finderPenaltyCountPatterns(r)}},{key:"finderPenaltyAddHistory",value:function(e,t){0==t[0]&&(e+=this.size),t.pop(),t.unshift(e)}}],[{key:"encodeText",value:function(e,r){var i=t.QrSegment.makeSegments(e);return n.encodeSegments(i,r)}},{key:"encodeBinary",value:function(e,r){var i=t.QrSegment.makeBytes(e);return n.encodeSegments([i],r)}},{key:"encodeSegments",value:function(e,t){var r,a,h=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,l=arguments.length>3&&void 0!==arguments[3]?arguments[3]:40,f=arguments.length>4&&void 0!==arguments[4]?arguments[4]:-1,c=5>=arguments.length||void 0===arguments[5]||arguments[5];if(n.MIN_VERSION>h||h>l||l>n.MAX_VERSION||-1>f||f>7)throw new RangeError("Invalid value");for(r=h;;r++){var d=8*n.getNumDataCodewords(r,t),v=u.getTotalBits(e,r);if(d>=v){a=v;break}if(r>=l)throw new RangeError("Data too long")}for(var m=0,g=[n.Ecc.MEDIUM,n.Ecc.QUARTILE,n.Ecc.HIGH];g.length>m;m++){var y=g[m];c&&a<=8*n.getNumDataCodewords(r,y)&&(t=y)}var E,C=[],M=i(e);try{for(M.s();!(E=M.n()).done;){var w=E.value;o(w.mode.modeBits,4,C),o(w.numChars,w.mode.numCharCountBits(r),C);var R,A=i(w.getData());try{for(A.s();!(R=A.n()).done;){var N=R.value;C.push(N)}}catch(e){A.e(e)}finally{A.f()}}}catch(e){M.e(e)}finally{M.f()}s(C.length==a);var k=8*n.getNumDataCodewords(r,t);s(k>=C.length),o(0,Math.min(4,k-C.length),C),o(0,(8-C.length%8)%8,C),s(C.length%8==0);for(var p=236;k>C.length;p^=253)o(p,8,C);for(var P=[];C.length>8*P.length;)P.push(0);return C.forEach((function(e,t){return P[t>>>3]|=e<<7-(7&t)})),new n(r,t,P,f)}},{key:"getNumRawDataModules",value:function(e){if(n.MIN_VERSION>e||e>n.MAX_VERSION)throw new RangeError("Version number out of range");var t=(16*e+128)*e+64;if(e>=2){var r=Math.floor(e/7)+2;t-=(25*r-10)*r-55,7>e||(t-=36)}return s(t>=208&&29648>=t),t}},{key:"getNumDataCodewords",value:function(e,t){return Math.floor(n.getNumRawDataModules(e)/8)-n.ECC_CODEWORDS_PER_BLOCK[t.ordinal][e]*n.NUM_ERROR_CORRECTION_BLOCKS[t.ordinal][e]}},{key:"reedSolomonComputeDivisor",value:function(e){if(1>e||e>255)throw new RangeError("Degree out of range");for(var t=[],r=0;e-1>r;r++)t.push(0);t.push(1);for(var i=1,o=0;e>o;o++){for(var a=0;t.length>a;a++)t[a]=n.reedSolomonMultiply(t[a],i),t.length>a+1&&(t[a]^=t[a+1]);i=n.reedSolomonMultiply(i,2)}return t}},{key:"reedSolomonComputeRemainder",value:function(e,t){var r,o=t.map((function(e){return 0})),a=i(e);try{var s=function(){var e=r.value^o.shift();o.push(0),t.forEach((function(t,r){return o[r]^=n.reedSolomonMultiply(t,e)}))};for(a.s();!(r=a.n()).done;)s()}catch(e){a.e(e)}finally{a.f()}return o}},{key:"reedSolomonMultiply",value:function(e,t){if(e>>>8!=0||t>>>8!=0)throw new RangeError("Byte out of range");for(var r=0,n=7;n>=0;n--)r=r<<1^285*(r>>>7),r^=(t>>>n&1)*e;return s(r>>>8==0),r}}]),n}();function o(e,t,r){if(0>t||t>31||e>>>t!=0)throw new RangeError("Value out of range");for(var n=t-1;n>=0;n--)r.push(e>>>n&1)}function a(e,t){return 0!=(e>>>t&1)}function s(e){if(!e)throw Error("Assertion error")}n.MIN_VERSION=1,n.MAX_VERSION=40,n.PENALTY_N1=3,n.PENALTY_N2=3,n.PENALTY_N3=40,n.PENALTY_N4=10,n.ECC_CODEWORDS_PER_BLOCK=[[-1,7,10,15,20,26,18,20,24,30,18,20,24,26,30,22,24,28,30,28,28,28,28,30,30,26,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,10,16,26,18,24,16,18,22,22,26,30,22,22,24,24,28,28,26,26,26,26,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28],[-1,13,22,18,26,18,24,18,22,20,24,28,26,24,20,30,24,28,28,26,30,28,30,30,30,30,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,17,28,22,16,22,28,26,26,24,28,24,28,22,24,24,30,28,28,26,28,30,24,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30]],n.NUM_ERROR_CORRECTION_BLOCKS=[[-1,1,1,1,1,1,2,2,2,2,4,4,4,4,4,6,6,6,6,7,8,8,9,9,10,12,12,12,13,14,15,16,17,18,19,19,20,21,22,24,25],[-1,1,1,1,2,2,4,4,4,5,5,5,8,9,9,10,10,11,13,14,16,17,17,18,20,21,23,25,26,28,29,31,33,35,37,38,40,43,45,47,49],[-1,1,1,2,2,4,4,6,6,8,8,8,10,12,16,12,17,16,18,21,20,23,23,25,27,29,34,34,35,38,40,43,45,48,51,53,56,59,62,65,68],[-1,1,1,2,4,4,4,5,6,8,8,11,11,16,16,18,16,19,21,25,25,25,34,30,32,35,37,40,42,45,48,51,54,57,60,63,66,70,74,77,81]],t.QrCode=n;var u=function(){function t(r,n,i){if(e(this,t),this.mode=void 0,this.numChars=void 0,this.bitData=void 0,this.mode=r,this.numChars=n,this.bitData=i,0>n)throw new RangeError("Invalid argument");this.bitData=i.slice()}return r(t,[{key:"getData",value:function(){return this.bitData.slice()}}],[{key:"makeBytes",value:function(e){var r,n=[],a=i(e);try{for(a.s();!(r=a.n()).done;){o(r.value,8,n)}}catch(e){a.e(e)}finally{a.f()}return new t(t.Mode.BYTE,e.length,n)}},{key:"makeNumeric",value:function(e){if(!t.isNumeric(e))throw new RangeError("String contains non-numeric characters");for(var r=[],n=0;e.length>n;){var i=Math.min(e.length-n,3);o(parseInt(e.substring(n,n+i),10),3*i+1,r),n+=i}return new t(t.Mode.NUMERIC,e.length,r)}},{key:"makeAlphanumeric",value:function(e){if(!t.isAlphanumeric(e))throw new RangeError("String contains unencodable characters in alphanumeric mode");var r,n=[];for(r=0;e.length>=r+2;r+=2){var i=45*t.ALPHANUMERIC_CHARSET.indexOf(e.charAt(r));o(i+=t.ALPHANUMERIC_CHARSET.indexOf(e.charAt(r+1)),11,n)}return e.length>r&&o(t.ALPHANUMERIC_CHARSET.indexOf(e.charAt(r)),6,n),new t(t.Mode.ALPHANUMERIC,e.length,n)}},{key:"makeSegments",value:function(e){return""==e?[]:t.isNumeric(e)?[t.makeNumeric(e)]:t.isAlphanumeric(e)?[t.makeAlphanumeric(e)]:[t.makeBytes(t.toUtf8ByteArray(e))]}},{key:"makeEci",value:function(e){var r=[];if(0>e)throw new RangeError("ECI assignment value out of range");if(128>e)o(e,8,r);else if(16384>e)o(2,2,r),o(e,14,r);else{if(e>=1e6)throw new RangeError("ECI assignment value out of range");o(6,3,r),o(e,21,r)}return new t(t.Mode.ECI,0,r)}},{key:"isNumeric",value:function(e){return t.NUMERIC_REGEX.test(e)}},{key:"isAlphanumeric",value:function(e){return t.ALPHANUMERIC_REGEX.test(e)}},{key:"getTotalBits",value:function(e,t){var r,n=0,o=i(e);try{for(o.s();!(r=o.n()).done;){var a=r.value,s=a.mode.numCharCountBits(t);if(a.numChars>=1<<s)return 1/0;n+=4+s+a.bitData.length}}catch(e){o.e(e)}finally{o.f()}return n}},{key:"toUtf8ByteArray",value:function(e){e=encodeURI(e);for(var t=[],r=0;e.length>r;r++)"%"!=e.charAt(r)?t.push(e.charCodeAt(r)):(t.push(parseInt(e.substring(r+1,r+3),16)),r+=2);return t}}]),t}();u.NUMERIC_REGEX=/^[0-9]*$/,u.ALPHANUMERIC_REGEX=/^[A-Z0-9 $%*+.\/:-]*$/,u.ALPHANUMERIC_CHARSET="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:",t.QrSegment=u}(o||(o={})),function(t){!function(t){var n=r((function t(r,n){e(this,t),this.ordinal=void 0,this.formatBits=void 0,this.ordinal=r,this.formatBits=n}));n.LOW=new n(0,1),n.MEDIUM=new n(1,0),n.QUARTILE=new n(2,3),n.HIGH=new n(3,2),t.Ecc=n}(t.QrCode||(t.QrCode={}))}(o||(o={})),function(t){!function(t){var n=function(){function t(r,n){e(this,t),this.modeBits=void 0,this.numBitsCharCount=void 0,this.modeBits=r,this.numBitsCharCount=n}return r(t,[{key:"numCharCountBits",value:function(e){return this.numBitsCharCount[Math.floor((e+7)/17)]}}]),t}();n.NUMERIC=new n(1,[10,12,14]),n.ALPHANUMERIC=new n(2,[9,11,13]),n.BYTE=new n(4,[8,16,16]),n.KANJI=new n(8,[8,10,12]),n.ECI=new n(7,[0,0,0]),t.Mode=n}(t.QrSegment||(t.QrSegment={}))}(o||(o={}));var a=o;export{a as qrcodegen};
