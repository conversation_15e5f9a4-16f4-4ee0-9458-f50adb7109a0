---
description: 
globs: 
alwaysApply: false
---
# 需求文档编写规范

## 文档目录结构
所有需求文档应放在 `.docs` 目录下，按功能模块分类存放：
```
.docs/
  - 功能模块1/
    - 子功能A.md
    - 子功能B.md
  - 功能模块2/
    - 子功能C.md
  - 归档/
    - 已完成功能.md
```

## 文档命名规范
- 文件名使用中文，清晰表达功能名称
- 使用`.md`格式
- 名称应简洁明了，例如：`日记功能优化.md`

## 文档内容结构
每个需求文档应包含以下部分：

### 1. 需求概述
简要描述功能需求的背景和目标，不超过5行。

### 2. 当前实现方式
描述现有系统的实现方式（如适用），包括存在的问题和局限性。

### 3. 目标实现方式
描述新的实现方式，明确说明改进点和预期效果。

### 4. 实现步骤
列出详细的实现步骤，每个步骤应包含：
- 步骤描述
- 涉及的文件
- 代码修改要点

### 5. 具体代码实现
提供关键代码片段示例，使用Markdown代码块，并标明语言：
```javascript
// 示例代码
function example() {
  // 实现逻辑
}
```

### 6. 注意事项
列出实现过程中需要注意的问题和潜在风险。

## 文档修改规范
1. 已完成的需求文档应移至`归档`目录
2. 修改文档时保留修改历史记录
3. 在文档顶部添加最后更新日期

