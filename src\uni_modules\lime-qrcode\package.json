{"id": "lime-qrcode", "displayName": "qrcode 二维码生成", "version": "0.2.3", "description": "一款全平台通用的二维码生成插件，支持uniapp/uniappx", "keywords": ["qrcode", "qr", "uvue", "生成图片", "二维码"], "repository": "", "engines": {"uni-app": "^4.41", "uni-app-x": "^4.61"}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": "305716444"}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "", "darkmode": "x", "i18n": "x", "widescreen": "x"}, "uni_modules": {"dependencies": ["lime-shared"], "encrypt": [], "platforms": {"cloud": {"tcb": "√", "aliyun": "√", "alipay": "√"}, "client": {"uni-app": {"vue": {"vue2": "√", "vue3": "√"}, "web": {"safari": "√", "chrome": "√"}, "app": {"vue": "√", "nvue": "√", "android": {"extVersion": "", "minVersion": "21"}, "ios": "√", "harmony": "√"}, "mp": {"weixin": "√", "alipay": "√", "toutiao": "√", "baidu": "√", "kuaishou": "√", "jd": "√", "harmony": "-", "qq": "√", "lark": "√"}, "quickapp": {"huawei": "-", "union": "-"}}, "uni-app-x": {"web": {"safari": "√", "chrome": "√"}, "app": {"android": {"extVersion": "", "minVersion": "21"}, "ios": "√", "harmony": "√"}, "mp": {"weixin": "√"}}}}}}