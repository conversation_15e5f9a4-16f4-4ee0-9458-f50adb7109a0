// 云对象教程: https://uniapp.dcloud.net.cn/uniCloud/cloud-obj
// jsdoc语法提示教程：https://ask.dcloud.net.cn/docs/#//ask.dcloud.net.cn/article/129

/**
 * 滴答清单 API 云函数
 * 封装滴答清单相关的 API 接口调用
 */
module.exports = {
	_before: function () { // 通用预处理器
		// 可在此添加通用的预处理逻辑，如日志记录等
	},

	/**
	 * 滴答清单密码登录
	 * @param {string} username 登录账户（邮箱或手机号）
	 * @param {string} password 登录密码
	 * @returns {object} 登录结果，包含用户信息和认证令牌
	 */
	async passwordLogin(username, password) {
		// 参数校验
		if (!username) {
			return {
				errCode: 'PARAM_IS_NULL',
				errMsg: '用户名不能为空'
			}
		}

		if (!password) {
			return {
				errCode: 'PARAM_IS_NULL',
				errMsg: '密码不能为空'
			}
		}

		try {
			// 调用滴答清单登录 API
			const loginUrl = 'https://api.dida365.com/api/v2/user/signon?wc=true&remember=true'
			const requestData = {
				username: username,
				password: password
			}

			console.log('开始调用滴答清单登录 API:', { username })

			// 发起 HTTP 请求
			const response = await uniCloud.httpclient.request(loginUrl, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
				},
				data: requestData,
				timeout: 10000 // 10秒超时
			})

			console.log('滴答清单 API 响应状态:', response.status)
			console.log('滴答清单 API 响应数据:', response.data)

			// 检查响应状态
			if (response.status !== 200) {
				return {
					errCode: 'API_ERROR',
					errMsg: `滴答清单 API 请求失败，状态码: ${response.status}`,
					details: response.data
				}
			}

			// 解析响应数据
			let responseData
			try {
				responseData = typeof response.data === 'string' ? JSON.parse(response.data) : response.data
			} catch (parseError) {
				console.error('解析响应数据失败:', parseError)
				return {
					errCode: 'PARSE_ERROR',
					errMsg: '解析登录响应数据失败',
					details: response.data
				}
			}

			// 检查登录是否成功（滴答清单成功响应会包含 token 字段）
			if (!responseData.token) {
				return {
					errCode: 'LOGIN_FAILED',
					errMsg: responseData.errorMessage || '登录失败，请检查用户名和密码',
					details: responseData
				}
			}

			// 登录成功，返回用户信息
			return {
				errCode: null,
				errMsg: '登录成功',
				data: {
					token: responseData.token,
					userId: responseData.userId,
					userCode: responseData.userCode,
					username: responseData.username,
					inboxId: responseData.inboxId,
					pro: responseData.pro,
					teamPro: responseData.teamPro,
					proStartDate: responseData.proStartDate,
					proEndDate: responseData.proEndDate,
					subscribeType: responseData.subscribeType,
					subscribeFreq: responseData.subscribeFreq,
					needSubscribe: responseData.needSubscribe,
					teamUser: responseData.teamUser,
					activeTeamUser: responseData.activeTeamUser,
					freeTrial: responseData.freeTrial,
					gracePeriod: responseData.gracePeriod,
					ds: responseData.ds
				}
			}

		} catch (error) {
			console.error('滴答清单登录接口调用异常:', error)

			// 处理不同类型的错误
			if (error.code === 'TIMEOUT') {
				return {
					errCode: 'REQUEST_TIMEOUT',
					errMsg: '请求超时，请稍后重试'
				}
			}

			if (error.response) {
				const status = error.response.status
				if (status === 401) {
					return {
						errCode: 'UNAUTHORIZED',
						errMsg: '用户名或密码错误'
					}
				}
				if (status === 403) {
					return {
						errCode: 'FORBIDDEN',
						errMsg: '账户被禁用或权限不足'
					}
				}
				if (status >= 500) {
					return {
						errCode: 'SERVER_ERROR',
						errMsg: '滴答清单服务器错误，请稍后重试'
					}
				}
			}

			return {
				errCode: 'UNKNOWN_ERROR',
				errMsg: error.message || '登录过程中发生未知错误',
				details: error
			}
		}
	}
}
