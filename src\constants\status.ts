/**
 * 系统状态常量定义
 * 统一管理所有状态值，避免硬编码和类型不一致问题
 */

// OKR 目标状态
export const OKR_STATUS = {
  PENDING: 'pending',        // 待开始
  IN_PROGRESS: 'inProgress', // 进行中
  COMPLETED: 'completed',    // 已完成
  PAUSED: 'paused',         // 暂停中
  ABANDONED: 'abandoned'     // 已放弃
} as const

// OKR 目标状态类型
export type OkrStatus = typeof OKR_STATUS[keyof typeof OKR_STATUS]

// OKR 状态显示标签映射
export const OKR_STATUS_LABELS = {
  [OKR_STATUS.PENDING]: '待开始',
  [OKR_STATUS.IN_PROGRESS]: '进行中',
  [OKR_STATUS.COMPLETED]: '已完成',
  [OKR_STATUS.PAUSED]: '暂停中',
  [OKR_STATUS.ABANDONED]: '已放弃'
} as const

// 任务状态
export const TASK_STATUS = {
  INCOMPLETE: 0,  // 未完成
  COMPLETED: 1,   // 已完成
  ABANDONED: 2    // 已放弃
} as const

// 任务状态类型
export type TaskStatus = typeof TASK_STATUS[keyof typeof TASK_STATUS]

// 任务状态显示标签映射
export const TASK_STATUS_LABELS = {
  [TASK_STATUS.INCOMPLETE]: '未完成',
  [TASK_STATUS.COMPLETED]: '已完成',
  [TASK_STATUS.ABANDONED]: '已放弃'
} as const

// 用户状态（如果需要）
export const USER_STATUS = {
  NORMAL: 0,      // 正常
  BANNED: 1,      // 已封禁
  AUDITING: 2,    // 审核中
  AUDIT_FAILED: 3,// 审核失败
  CLOSED: 4       // 已关闭
} as const

// 用户状态类型
export type UserStatus = typeof USER_STATUS[keyof typeof USER_STATUS]

// 状态工具函数
export const StatusUtils = {
  // 检查OKR是否为活跃状态（可以显示任务的状态）
  isOkrActive: (status: OkrStatus): boolean => {
    return status === OKR_STATUS.IN_PROGRESS
  },

  // 检查OKR是否已完成
  isOkrCompleted: (status: OkrStatus): boolean => {
    return status === OKR_STATUS.COMPLETED
  },

  // 检查OKR是否暂停
  isOkrPaused: (status: OkrStatus): boolean => {
    return status === OKR_STATUS.PAUSED
  },

  // 检查任务是否已完成
  isTaskCompleted: (status: TaskStatus): boolean => {
    return status === TASK_STATUS.COMPLETED
  },

  // 获取OKR状态显示文本
  getOkrStatusLabel: (status: OkrStatus): string => {
    return OKR_STATUS_LABELS[status] || '未知状态'
  },

  // 获取任务状态显示文本
  getTaskStatusLabel: (status: TaskStatus): string => {
    return TASK_STATUS_LABELS[status] || '未知状态'
  },

  // 构建数据库查询条件
  buildOkrStatusQuery: {
    // 只查询进行中的目标
    inProgress: () => `status === "${OKR_STATUS.IN_PROGRESS}"`,
    
    // 排除暂停的目标
    excludePaused: () => `status !== "${OKR_STATUS.PAUSED}"`,
    
    // 排除已放弃的目标
    excludeAbandoned: () => `status !== "${OKR_STATUS.ABANDONED}"`,
    
    // 查询活跃目标（进行中 + 待开始）
    active: () => `status === "${OKR_STATUS.IN_PROGRESS}" || status === "${OKR_STATUS.PENDING}"`,
    
    // 查询已结束目标（已完成 + 已放弃）
    finished: () => `status === "${OKR_STATUS.COMPLETED}" || status === "${OKR_STATUS.ABANDONED}"`
  }
}

// 筛选标签配置（用于UI组件）
export const OKR_FILTER_TABS = [
  { label: OKR_STATUS_LABELS[OKR_STATUS.IN_PROGRESS], value: OKR_STATUS.IN_PROGRESS },
  { label: OKR_STATUS_LABELS[OKR_STATUS.PENDING], value: OKR_STATUS.PENDING },
  { label: OKR_STATUS_LABELS[OKR_STATUS.COMPLETED], value: OKR_STATUS.COMPLETED },
  { label: OKR_STATUS_LABELS[OKR_STATUS.PAUSED], value: OKR_STATUS.PAUSED },
  { label: OKR_STATUS_LABELS[OKR_STATUS.ABANDONED], value: OKR_STATUS.ABANDONED }
] as const
