## 0.4.1（2025-06-13）
- fix：测试hbx4.71更新
## 0.4.0（2025-06-04）
- fix：测试hbx4.71更新
## 0.3.9（2025-06-04）
 - fix: 因hbx4.71更新导致mac用户下载的文件被重命名，故退回4.66
## 0.3.8（2025-06-03）
- fix: 修复`while`在vue2报错的问题
## 0.3.7（2025-05-21）
- fix: 修复`merge`在vue2报错的问题
## 0.3.6（2025-05-17）
- fix: 修复`raf`导错目录的问题
## 0.3.5（2025-05-16）
- feat: 增加`isIP`
- feat: 增加`merge`
- feat: 增加`isByteLength`
- feat: 增加`isRegExp`
## 0.3.4（2025-04-27）
- fix: 修复exif缺少isBase64的问题
## 0.3.3（2025-04-14）
- fix: 修复4.61上的类型问题
## 0.3.2（2025-04-14）
- fix: 修复4.61上的类型问题
## 0.3.1（2025-03-28）
- fix: 修复getRect在nvue上的问题
## 0.3.0（2025-02-27）
- fix: 修复部分函数无法在uniappx上运行的问题
## 0.2.9（2025-02-19）
- chore: 更新文档
## 0.2.8（2025-02-11）
- chore: 更新文档
## 0.2.7（2025-01-17）
- fix: 针对canvas 平台判断优化
## 0.2.6（2025-01-09）
- feat: 增加`areaData`中国省市区数据
## 0.2.5（2025-01-07）
- fix: animation在app上类型问题
## 0.2.4（2025-01-04）
- feat: getRect类型问题
## 0.2.3（2025-01-01）
- chore: unitConvert使用uni.rpx2px
## 0.2.2（2024-12-11）
- chore: 动画使用`requestAnimationFrame`
## 0.2.1（2024-11-20）
- feat: 增加`characterLimit`
## 0.2.0（2024-11-14）
- fix: vue2的类型问题
## 0.1.9（2024-11-14）
- feat: 增加`shuffle`
## 0.1.8（2024-10-08）
- fix: vue2 条件编译 // #ifdef APP-IOS || APP-ANDROID 会生效
## 0.1.7（2024-09-23）
- fix: raf 类型跟随版本变更
## 0.1.6（2024-07-24）
- fix: vue2 app ts需要明确的后缀，所有补全
- chore: 减少依赖
## 0.1.5（2024-07-21）
- feat: 删除 Hooks
- feat: 兼容uniappx
## 0.1.4（2023-09-05）
- feat: 增加 Hooks `useIntersectionObserver`
- feat: 增加 `floatAdd`
- feat: 因为本人插件兼容 vue2 需要使用 `composition-api`，故增加vue文件代码插件的条件编译
## 0.1.3（2023-08-13）
- feat: 增加 `camelCase`
## 0.1.2（2023-07-17）
- feat: 增加 `getClassStr`
## 0.1.1（2023-07-06）
- feat: 增加 `isNumeric`， 区别于 `isNumber`
## 0.1.0（2023-06-30）
- fix: `clamp`忘记导出了
## 0.0.9（2023-06-27）
- feat: 增加`arrayBufferToFile`
## 0.0.8（2023-06-19）
- feat: 增加`createAnimation`、`clamp`
## 0.0.7（2023-06-08）
- chore: 更新注释
## 0.0.6（2023-06-08）
- chore: 增加`createImage`为`lime-watermark`和`lime-qrcode`提供依赖
## 0.0.5（2023-06-03）
- chore: 更新注释
## 0.0.4（2023-05-22）
- feat: 增加`range`,`exif`,`selectComponent`
## 0.0.3（2023-05-08）
- feat: 增加`fillZero`,`debounce`,`throttle`,`random`
## 0.0.2（2023-05-05）
- chore: 更新文档
## 0.0.1（2023-05-05）
- 无
