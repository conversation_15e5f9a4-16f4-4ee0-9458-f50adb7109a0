# 普通话训练单字分类显示需求

## 背景

当前普通话训练页面的单字输入功能缺乏分类标识，用户无法直观地了解单字的发音特点。为了提升用户的学习体验，需要为单字添加简单的分类显示功能，帮助用户更好地识别不同类型的发音练习。

该功能将帮助用户：
- 直观识别单字的发音分类
- 更好地理解发音特点
- 提升学习的针对性

## 需求

### 功能需求

#### 1. 分类选择功能
- 在单字输入弹窗中添加简单的分类选择
- 支持的分类包括：
  - 平舌音（z、c、s）
  - 翘舌音（zh、ch、sh、r）
  - 前鼻音（an、en、in、un、ün）
  - 后鼻音（ang、eng、ing、ong）
- 分类选择为必选项，确保每个单字都有对应的分类

#### 2. 分类展示功能
- 在单字列表/卡片中显示分类标签
- 分类标签具有良好的视觉区分度：
  - 平舌音：蓝色标签
  - 翘舌音：绿色标签
  - 前鼻音：橙色标签
  - 后鼻音：紫色标签
- 标签样式与现有 UI 风格保持一致

### 非功能需求

#### 用户体验
- 分类选择操作简单直观
- 分类标签视觉效果清晰易识别
- 与现有交互模式保持一致

#### 兼容性
- 支持移动端和 PC 端
- 兼容现有的数据存储格式
- 向后兼容已有的单字数据

## 技术方案

### 实现思路

#### 1. 数据模型调整
```typescript
// 在现有 Content 字段中扩展分类信息
interface Content {
  type: 'putonghuaCharacter';
  word: string;
  category: WordCategory; // 新增分类字段
  // 其他现有字段...
}

// 分类枚举
enum WordCategory {
  FLAT_TONGUE = 'flat_tongue',    // 平舌音
  CURVED_TONGUE = 'curved_tongue', // 翘舌音
  FRONT_NASAL = 'front_nasal',    // 前鼻音
  BACK_NASAL = 'back_nasal'       // 后鼻音
}
```

#### 2. 组件架构设计
```mermaid
graph TD
    A[普通话训练页面] --> B[单字输入弹窗]
    A --> C[单字展示区域]
    B --> D[分类选择下拉框]
    C --> F[单字卡片组件]
    F --> G[分类标签显示]

    H[数据管理层] --> I[单字数据服务]
    I --> J[本地存储]

    D --> I
    G --> I
```

#### 3. 实现方案

##### 分类选择功能
- 在单字输入弹窗中添加简单的下拉选择框
- 使用现有的 UI 组件库（如 select 组件）
- 无需创建独立的组件

##### 分类标签显示
- 在单字卡片中直接显示分类标签
- 使用简单的 span 标签配合 CSS 样式
- 根据分类类型应用不同的颜色样式

#### 4. 分类配置方案
```typescript
// 分类常量定义
const CATEGORY_CONFIG = {
  [WordCategory.FLAT_TONGUE]: {
    label: '平舌音',
    color: '#1890ff',
    description: 'z、c、s 音'
  },
  [WordCategory.CURVED_TONGUE]: {
    label: '翘舌音',
    color: '#52c41a',
    description: 'zh、ch、sh、r 音'
  },
  [WordCategory.FRONT_NASAL]: {
    label: '前鼻音',
    color: '#fa8c16',
    description: 'an、en、in、un、ün 音'
  },
  [WordCategory.BACK_NASAL]: {
    label: '后鼻音',
    color: '#722ed1',
    description: 'ang、eng、ing、ong 音'
  }
};
```

### 技术栈与约束

#### 技术栈
- 前端框架：Vue.js 3 + TypeScript
- UI 组件库：现有组件库（保持一致性）
- 状态管理：Pinia/Vuex（根据项目现有方案）
- 数据存储：LocalStorage/IndexedDB（根据现有存储方案）

#### 开发约束
- 类型定义：在现有类型文件中扩展分类字段
- 样式规范：使用现有的设计系统和 CSS 变量
- 数据迁移：确保现有数据的平滑迁移
- 简化实现：使用现有 UI 组件，避免创建复杂的自定义组件

## 风险评估

### 假设与未知因素
- 假设用户主要使用移动端进行练习
- 假设现有单字数据量不会超过 1000 条
- 未知用户对分类准确性的要求程度

### 潜在风险

#### 1. 数据迁移风险
- **风险**：现有单字数据缺少分类信息
- **影响**：可能导致数据不完整或用户体验中断
- **解决方案**：
  - 实现数据迁移脚本，为现有数据设置默认分类
  - 渐进式迁移，允许用户逐步完善分类信息

#### 2. 分类准确性风险
- **风险**：用户可能对发音分类不够了解，导致分类错误
- **影响**：影响训练效果和用户体验
- **解决方案**：
  - 提供分类说明和示例
  - 允许用户修改和调整分类

#### 3. 兼容性风险
- **风险**：新功能可能与现有功能产生冲突
- **影响**：功能异常或数据丢失
- **解决方案**：
  - 充分的回归测试
  - 渐进式功能发布
