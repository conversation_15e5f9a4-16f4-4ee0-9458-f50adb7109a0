# 周目标和周复盘功能需求

status: draft

## 背景
目前应用已实现了日回顾功能，但缺乏周目标和周复盘相关功能。用户在管理个人 OKR 和任务时，需要有周级别的计划和复盘功能，以便更全面地管理工作和学习进度。通过增加周目标和周复盘功能，能够让用户对一周的工作进行更系统的规划和回顾，提高工作和学习效率。

## 需求

### 功能需求

1. **周概览弹窗扩展**
   - 将现有的"周概览"弹窗扩展为包含三个标签页：周目标、周概览、周复盘
   - 三个标签页可以通过点击切换

2. **周目标功能**
   - 在周目标标签页中展示用户为当前周制定的计划内容
   - 提供编辑按钮，点击后跳转到专门的周目标编辑页面
   - 自动保存编辑内容

3. **周复盘功能**
   - 在周复盘标签页中展示用户对当前周的复盘内容
   - 提供编辑按钮，点击后跳转到专门的周复盘编辑页面
   - 提供复盘模板，包括"本周成就"、"遇到的挑战"、"下周改进"等部分

4. **AI 辅助功能**
   - 保存周目标时，调用 AI 接口生成结构化的周概览内容
   - 每次添加日回顾时，调用 AI 接口根据日回顾内容调整周概览内容
   - 周概览展示 AI 提取的目标内容，包括目标名称、进度、风险评估和目标数量等

### 非功能需求

1. **用户体验**
   - 界面设计与现有 APP 风格保持一致
   - 操作流程简单直观，降低用户使用门槛

## 技术方案

### 实现思路

1. **数据模型设计**
   - 利用现有的 `memo` 数据表存储周目标和周复盘数据
   - 通过 `type` 字段区分不同类型的记录：
     - 使用 `weekGoal` 表示周目标
     - 使用 `weekReview` 表示周复盘
     - 使用 `weekOverview` 表示 AI 生成的周概览结构化数据
   - 通过 `date` 字段存储周数，以数字形式表示，如 `25` 表示今年第 25 周
   - 查询特定周的数据也通过该字段进行，如查询第 25 周的周目标和周复盘

2. **UI 实现**
   - 扩展现有 `/pages/okr/today.vue` 中的周概览弹窗组件，添加标签页切换功能
   - 创建一个新页面：`/pages/okr/weekEdit.vue` 用于编辑周目标和周复盘，通过 URL 参数区分不同的编辑类型
   - 为现有组件添加周目标和周复盘的编辑入口

3. **组件交互流程**
```mermaid
graph TD
    A[today.vue 周概览按钮] --> B[扩展的周概览弹窗]
    B --> C[标签页: 周目标]
    B --> D[标签页: 周概览]
    B --> E[标签页: 周复盘]
    C --> F[编辑按钮]
    E --> G[编辑按钮]
    F --> H[weekEdit.vue?type=goal 编辑页面]
    G --> I[weekEdit.vue?type=review 编辑页面]
    H --> J[保存数据]
    J --> M[调用 AI 接口]
    M --> N[生成周概览数据]
    N --> C
    I --> K[保存数据]
    K --> E
    O[日回顾保存] --> P[调用 AI 接口]
    P --> Q[更新周概览数据]
    Q --> D
```

### 技术栈与约束

1. **前端开发**
   - 使用 Font Awesome 图标库提供界面图标
   - 标签页切换使用 CSS 过渡效果提升用户体验
   - 周目标和周复盘编辑器使用简单文本编辑器，无需富文本功能

2. **数据存储**
   - 使用现有的 `memo` 表结构存储数据
   - 通过 `type` 字段区分不同类型的记录
   - 通过 `date` 字段存储周数，如 `25` 表示第 25 周
   - 通过 `content` 字段存储内容
   - 通过 `aiAnalysis` 字段存储 AI 生成的结构化数据

3. **AI 接口设计**
   - 创建模拟 AI 函数替代真实 AI 接口
   - 设计两个主要函数：
     - `generateWeekOverview(weekGoalContent)`: 根据周目标生成初始周概览
     - `updateWeekOverview(dailyReviewContent, currentWeekOverview)`: 根据日回顾更新周概览

### 具体实现步骤

1. 扩展现有周概览弹窗：
   - 修改 `/pages/okr/today.vue` 中的周概览弹窗，添加标签页切换组件
   - 保留原有周概览内容作为中间标签页
   - 添加周目标和周复盘标签页的布局和编辑按钮

2. 创建周目标和周复盘共用编辑页面：
   - 创建 `weekEdit.vue` 页面
   - 通过 URL 参数 `?type=goal` 或 `?type=review` 区分编辑周目标或周复盘
   - 根据不同的类型加载不同的初始内容和提示信息
   - 实现简单文本编辑功能
   - 添加自动保存和手动保存功能
   - 保存时调用相应的 AI 模拟函数

3. 实现 AI 模拟函数：
   - 直接在相关组件或工具文件中实现普通函数
   - 实现两个函数：`generateWeekOverview` 和 `updateWeekOverview`
   - 定义周概览数据的结构和格式

4. 修改日回顾功能：
   - 更新日回顾保存逻辑，添加对周概览数据的更新

## 风险评估

### 假设与未知因素
- 假设现有的 memo 表结构可以满足存储周目标和周复盘数据的需求
- 假设模拟 AI 函数可以在后期平滑过渡到真实 AI 接口
- 未知用户对周目标和复盘的具体需求差异化程度