# OKR 状态管理最佳实践指南

## 概述

本指南介绍了如何在 OKR 系统中正确使用统一的状态管理，避免状态值不一致导致的问题。

## 1. 状态常量使用

### 导入状态常量

```typescript
import { OKR_STATUS, TASK_STATUS, StatusUtils } from '@/constants/status'
```

### OKR 状态使用示例

```typescript
// ✅ 正确：使用状态常量
const activeOkrs = okrs.filter(okr => okr.status === OKR_STATUS.IN_PROGRESS)

// ❌ 错误：硬编码状态值
const activeOkrs = okrs.filter(okr => okr.status === 'inProgress')
```

### 任务状态使用示例

```typescript
// ✅ 正确：使用状态常量
const completedTasks = tasks.filter(task => task.status === TASK_STATUS.COMPLETED)

// ❌ 错误：硬编码状态值
const completedTasks = tasks.filter(task => task.status === 1)
```

## 2. 状态工具函数

### 使用 StatusUtils 进行状态判断

```typescript
// ✅ 推荐：使用工具函数
const isActive = StatusUtils.isOkrActive(okr.status)
const isPaused = StatusUtils.isOkrPaused(okr.status)
const isCompleted = StatusUtils.isTaskCompleted(task.status)

// ✅ 获取状态显示文本
const statusLabel = StatusUtils.getOkrStatusLabel(okr.status)
```

## 3. 数据库查询

### 使用类型安全的查询构建器

```typescript
import { buildOkrQuery, buildTaskQuery } from '@/utils/queryBuilder'

// ✅ 正确：使用查询构建器
const inProgressOkrs = await getOkrListApi(buildOkrQuery.inProgress())
const incompleteTasks = await getTaskListApi(buildTaskQuery.incomplete())

// ❌ 错误：硬编码查询条件
const inProgressOkrs = await getOkrListApi('status === "inProgress"')
```

### 复杂查询示例

```typescript
import { buildComplexQuery } from '@/utils/queryBuilder'

// 查询指定日期范围内的活跃任务
const tasks = await getTaskListApi(
  buildComplexQuery.tasksInDateRange('2024-01-01', '2024-01-31')
)
```

## 4. 组件中的状态处理

### Vue 组件示例

```vue
<script setup>
import { OKR_STATUS, StatusUtils } from '@/constants/status'

// ✅ 正确：使用状态常量进行初始化
const activeFilter = ref(OKR_STATUS.IN_PROGRESS)

// ✅ 正确：使用工具函数进行状态判断
const filteredTasks = computed(() => {
  return tasks.value.filter(task => {
    return !task.target || StatusUtils.isOkrActive(task.target.status)
  })
})
</script>
```

## 5. API 调用最佳实践

### 状态更新

```typescript
import { OKR_STATUS } from '@/constants/status'
import type { OkrStatusUpdate } from '@/types/database'

// ✅ 正确：使用类型安全的状态更新
const updateData: OkrStatusUpdate = {
  status: OKR_STATUS.PAUSED
}
await updateOkrApi(okrId, updateData)
```

### 状态验证

```typescript
import { isValidOkrStatus, isValidTaskStatus } from '@/types/database'

// ✅ 正确：在 API 调用前验证状态值
if (isValidOkrStatus(newStatus)) {
  await updateOkrApi(okrId, { status: newStatus })
} else {
  throw new Error(`Invalid OKR status: ${newStatus}`)
}
```

## 6. 类型安全检查

### TypeScript 配置

确保 `tsconfig.json` 中启用了严格的类型检查：

```json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "exactOptionalPropertyTypes": true,
    "noUncheckedIndexedAccess": true
  }
}
```

### ESLint 规则

使用 ESLint 规则检查状态值使用：

```javascript
// .eslintrc.js
{
  "rules": {
    "@typescript-eslint/no-magic-numbers": ["warn", {
      "ignore": [0, 1, 2, -1]
    }],
    "eqeqeq": ["error", "always"]
  }
}
```

## 7. 常见错误和解决方案

### 错误 1：状态值类型不匹配

```typescript
// ❌ 错误：混用字符串和数字状态值
if (okr.status === 2) { // 数字 2
  // 处理暂停状态
}

// ✅ 正确：使用统一的状态常量
if (okr.status === OKR_STATUS.PAUSED) { // 字符串 'paused'
  // 处理暂停状态
}
```

### 错误 2：查询条件硬编码

```typescript
// ❌ 错误：硬编码查询条件
const okrs = await getOkrListApi('status != 2')

// ✅ 正确：使用查询构建器
const okrs = await getOkrListApi(buildOkrQuery.notPaused())
```

### 错误 3：缺少状态验证

```typescript
// ❌ 错误：直接使用未验证的状态值
await updateOkrApi(okrId, { status: userInput })

// ✅ 正确：验证后使用
if (isValidOkrStatus(userInput)) {
  await updateOkrApi(okrId, { status: userInput })
} else {
  throw new Error('Invalid status value')
}
```

## 8. 迁移指南

### 从硬编码状态值迁移

1. **替换硬编码字符串**：
   ```typescript
   // 替换前
   status === 'inProgress'
   
   // 替换后
   status === OKR_STATUS.IN_PROGRESS
   ```

2. **替换硬编码数字**：
   ```typescript
   // 替换前
   task.status === 1
   
   // 替换后
   task.status === TASK_STATUS.COMPLETED
   ```

3. **更新查询条件**：
   ```typescript
   // 替换前
   getOkrListApi('status != "paused"')
   
   // 替换后
   getOkrListApi(buildOkrQuery.notPaused())
   ```

### 批量替换工具

可以使用以下正则表达式进行批量替换：

- 查找：`status\s*===\s*['"]inProgress['"]`
- 替换：`status === OKR_STATUS.IN_PROGRESS`

## 9. 测试建议

### 单元测试示例

```typescript
import { OKR_STATUS, StatusUtils } from '@/constants/status'

describe('状态管理', () => {
  test('应该正确识别活跃状态', () => {
    expect(StatusUtils.isOkrActive(OKR_STATUS.IN_PROGRESS)).toBe(true)
    expect(StatusUtils.isOkrActive(OKR_STATUS.PAUSED)).toBe(false)
  })
  
  test('查询构建器应该生成正确的条件', () => {
    const condition = buildOkrQuery.inProgress()
    expect(condition).toBe('status === "inProgress"')
  })
})
```

通过遵循这些最佳实践，可以确保 OKR 系统中的状态管理保持一致性和类型安全性。
