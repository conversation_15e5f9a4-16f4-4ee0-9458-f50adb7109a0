<template>
  <u-popup v-model="show" mode="bottom" border-radius="24" :safe-area-inset-bottom="true" @close="onClose">
    <view class="p-5 bg-white">
      <!-- 标签选择区域 -->
      <view class="mb-5">
        <text class="text-sm text-gray-500 mb-2 block">选择标签</text>
        <view class="flex flex-wrap gap-2">
          <view
            v-for="(tag, index) in tags"
            :key="index"
            class="px-4 py-1.5 rounded-full text-sm transition-all duration-300 transform"
            :class="{
              'bg-gray-100 text-gray-600 hover:bg-gray-200': !selectedTags.includes(tag),
              'bg-blue-500/90 text-white shadow-sm scale-105 hover:bg-blue-600': selectedTags.includes(tag),
            }"
            @click="toggleTag(tag)"
            @longpress="handleLongPress(tag)"
          >
            {{ tag }}
          </view>
          <view
            class="px-4 py-1.5 rounded-full text-sm transition-all duration-300 bg-gray-100 text-gray-600 hover:bg-gray-200 flex items-center justify-center"
            @click="openAddTagDialog"
          >
            <text class="font-bold">+</text>
          </view>
        </view>
      </view>

      <!-- 输入区域 -->
      <view class="bg-gray-50 rounded-xl shadow-sm border border-gray-100 mb-5 overflow-hidden">
        <u-input
          v-model="content"
          border="none"
          maxlength="5000"
          type="textarea"
          placeholder="今天发生了什么有趣的事情......"
          class="w-full"
        />
      </view>

      <u-button
        class="mb-2 shadow-sm"
        type="primary"
        shape="circle"
        @click="onSubmit"
        :custom-style="{
          background: 'linear-gradient(to right, #4f46e5, #6366f1)',
          color: '#ffffff',
          fontWeight: 'bold',
          border: 'none',
        }"
      >
        保存日记
      </u-button>
    </view>
  </u-popup>
</template>

<script setup>
import { getTagListApi, addDiaryApi, updateDiaryApi, delMemoApi } from '@/api/memo'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  initialContent: {
    type: String,
    default: '',
  },
  currentDate: {
    type: String,
    default: '',
  },
  diaryId: {
    type: String,
    default: '',
  },
  initialTags: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:visible', 'close', 'submit'])

const show = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val),
})

const tags = ref([])
const selectedTags = ref([])
const content = ref('')

watch(
  () => props.initialContent,
  (val) => {
    content.value = val
  },
  { immediate: true }
)

watch(
  () => props.initialTags,
  (val) => {
    if (val && val.length > 0) {
      selectedTags.value = [...val]
    }
  },
  { immediate: true }
)

const toggleTag = (tag) => {
  const index = selectedTags.value.indexOf(tag)
  if (index === -1) {
    selectedTags.value.push(tag)
  } else {
    selectedTags.value.splice(index, 1)
  }
}

const handleLongPress = (tag) => {
  uni.showModal({
    title: '标签操作',
    editable: true,
    content: tag,
    placeholderText: '请输入新标签内容',
    showCancel: true,
    cancelText: '删除',
    confirmText: '保存',
    success: async ({ confirm, cancel, content }) => {
      if (cancel) {
        const tagList = await getTagListApi()
        const t = tagList.find((item) => item.content === tag)
        if (t) {
          await delMemoApi(t._id)
          getTagList()
          // 清除已选中的已删除标签
          selectedTags.value = selectedTags.value.filter((t) => t !== tag)
        }
      }
      if (confirm && content && content !== tag) {
        const tagList = await getTagListApi()
        const t = tagList.find((item) => item.content === tag)
        if (t) {
          await updateDiaryApi(t._id, { content })
          getTagList()
        }
      }
    },
  })
}

const openAddTagDialog = () => {
  uni.showModal({
    title: '新建标签',
    editable: true,
    placeholderText: '请输入标签内容',
    success: ({ confirm, content }) => {
      if (confirm && content) {
        if (!tags.value.includes(content)) {
          addDiaryApi({ content, type: 'tag' }).then(() => {
            getTagList()
          })
        } else {
          uni.showToast({ title: '标签已存在', icon: 'none' })
        }
      }
    },
  })
}

const onSubmit = () => {
  if (!content.value) {
    uni.showToast({
      title: '请输入内容',
      icon: 'none',
    })
    return
  }

  emit('submit', {
    content: content.value,
    selectedTags: selectedTags.value,
    diaryId: props.diaryId,
  })
}

const onClose = () => {
  emit('close', {
    selectedTags: selectedTags.value
  })
}

const getTagList = async () => {
  const res = await getTagListApi()
  tags.value = res.map((item) => item.content)
}

onMounted(() => {
  getTagList()
})
</script>

<style scoped>
:deep(.u-textarea-class) {
  min-height: 220rpx;
  padding: 24rpx;
  line-height: 1.8;
  font-size: 28rpx;
  background-color: transparent;
}

:deep(.u-popup) {
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.05);
}
</style>
