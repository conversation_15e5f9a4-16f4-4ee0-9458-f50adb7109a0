# 日记模块代码重构提示词

## 需求概述
需要将日记功能的 AI 分析结果存储方式从"多条数据方式"改为"JSON 存储方式"，以提高数据一致性、查询效率并减少数据库操作次数。

## 当前实现方式
目前日记功能的实现使用多条数据方式存储 AI 分析结果：
1. 用户提交日记内容，调用 AI 分析服务
2. 将 AI 分析结果按标签拆分为多条数据存储
3. 展示时，根据日期获取原始日记内容和对应的 AI 分析内容

## 目标实现方式
改为使用 JSON 存储方式：
1. 在`memo`表中添加`aiAnalysis`和`tagsList`字段
2. 将整个 AI 分析结果以 JSON 格式存储在单个字段中
3. 将标签列表单独存储便于快速筛选
4. 开发数据迁移脚本确保平滑过渡
5. 更新 UI 组件，保持 API 接口不变

## 重构步骤

### 1. 数据库结构更新
首先需要修改数据库表结构，添加新字段：
- 更新 `src/api/dataSchema/index.ts` 添加 `aiAnalysis` 和 `tagsList` 字段
- 更新 `src/api/dataSchema/typings.d.ts` 添加相应的类型定义

### 2. 编写迁移脚本
创建数据迁移脚本，将现有的 AI 分析数据迁移到新结构：
- 创建 `src/scripts/migrateDiaryData.js` 
- 实现数据迁移逻辑，将旧格式转换为新格式
- 在应用启动时检查版本并执行迁移，弹窗询问用户是否迁移

### 3. 更新 UI 组件
调整日记提交和展示相关的界面组件：
- 修改 `diary.vue` 中的日记提交函数 `handleDiarySubmit`
- 更新 AI 分析结果展示逻辑
- 确保标签筛选功能正常工作

## 具体代码实现

### 1. 更新数据库表结构
```javascript
// src/api/dataSchema/index.ts
memo: {
  // 现有字段...
  aiAnalysis: {
    aType: 'TEXT',
    defaultVal: '',
  },
  tagsList: {
    aType: 'TEXT',
    defaultVal: '',
  }
}
```

```typescript
// src/api/dataSchema/typings.d.ts
interface Memo {
  // 现有字段...
  aiAnalysis?: string; // JSON 字符串存储所有 AI 分析结果
  tagsList?: string; // JSON 字符串存储标签列表
}
```

### 2. 数据迁移脚本
```javascript
// src/scripts/migrateDiaryData.js
const migrateDiaryData = async () => {
  try {
    console.log('开始数据迁移...');
    
    // 获取所有日记记录
    const allDiaries = await db.table('memo')
      .where('type == "diary"')
      .toArray();
    
    console.log(`找到 ${allDiaries.length} 条日记记录`);
    
    // 为每条日记处理 AI 分析内容
    for (const diary of allDiaries) {
      // 获取该日记的所有 AI 分析记录
      const aiRecords = await db.table('memo')
        .where(`type == "aiDiary" && date == "${diary.date}"`)
        .toArray();
      
      if (aiRecords.length === 0) {
        console.log(`日记 ${diary._id} (${diary.date}) 没有 AI 分析记录，跳过`);
        continue;
      }
      
      // 构建 AI 分析 JSON
      const aiAnalysis = {};
      const tagsList = [];
      
      for (const record of aiRecords) {
        if (record.parentId === 'title') {
          aiAnalysis['标题'] = record.content;
        } else {
          // 查找标签名称
          const tag = await db.table('memo').get(record.parentId);
          if (tag && tag.content) {
            aiAnalysis[tag.content] = record.content;
            tagsList.push(tag.content);
          }
        }
      }
      
      // 更新日记记录
      await db.table('memo').update(diary._id, {
        aiAnalysis: JSON.stringify(aiAnalysis),
        tagsList: JSON.stringify(tagsList)
      });
      
      console.log(`日记 ${diary._id} (${diary.date}) 迁移完成，包含 ${tagsList.length} 个标签`);
    }
    
    console.log('数据迁移完成！');
    return { success: true, message: '迁移成功' };
  } catch (error) {
    console.error('数据迁移失败：', error);
    return { success: false, error };
  }
};
```

### 3. 更新 UI 组件
```javascript
// pages/memo/diary.vue

// 在 diary.vue 中添加直接操作数据库的方法
const saveDiaryWithAnalysis = async (params) => {
  const { content, date, aiAnalysis, diaryId, tagsList } = params;
  
  const diaryData = {
    content,
    date,
    type: 'diary',
    aiAnalysis: typeof aiAnalysis === 'string' ? aiAnalysis : JSON.stringify(aiAnalysis),
    tagsList: typeof tagsList === 'string' ? tagsList : JSON.stringify(tagsList)
  };
  
  if (diaryId) {
    return await db.table('memo').update(diaryId, diaryData);
  } else {
    return await db.table('memo').add(diaryData);
  }
};

// 在组件中处理 JSON 提取逻辑
const processAiAnalysis = async (diary) => {
  if (!diary || !diary.aiAnalysis) return [];
  
  try {
    const aiAnalysisObj = JSON.parse(diary.aiAnalysis);
    const tagList = await getTagListApi();
    const result = [];
    
    // 处理标题
    if (aiAnalysisObj['标题']) {
      result.push({
        content: aiAnalysisObj['标题'],
        parentId: 'title',
        type: 'aiDiary',
        tag: '标题',
        date: diary.date
      });
    }
    
    // 处理标签内容
    for (const tagName in aiAnalysisObj) {
      if (tagName === '标题') continue;
      
      const tag = tagList.find(t => t.content === tagName);
      if (tag) {
        result.push({
          content: aiAnalysisObj[tagName],
          parentId: tag._id,
          tag: tagName,
          type: 'aiDiary',
          date: diary.date
        });
      }
    }
    
    return result;
  } catch (e) {
    console.error('解析 AI 分析数据失败', e);
    return [];
  }
};

// 获取日记数据
const getDay = async (date = '') => {
  if (!date) {
    date = dayjs().format('YYYY-MM-DD');
  }
  console.log('getDay', date);
  showDiaryContent.value = false;
  showInputPopup.value = false; // 关闭底部输入框
  
  // 设置当前日期
  curDay.value = date;

  const data = await getDiaryApi(curDay.value);
  
  // 重置编辑表单，避免数据混淆
  editForm.value = { content: '' };

  if (data?.content) {
    // 只更新显示数据
    memoForm.value = data;
    title.value = '';
    
    // 处理 AI 分析数据
    if (data.aiAnalysis) {
      // 新版：从 JSON 字段中提取 AI 分析结果
      const processedAiData = await processAiAnalysis(data);
      
      // 设置标题
      const titleItem = processedAiData.find(item => item.parentId === 'title');
      if (titleItem) {
        title.value = titleItem.content;
      }
      
      // 过滤掉标题项，只保留标签分析内容
      aiList.value = processedAiData.filter(item => item.parentId !== 'title');
    } else {
      // 兼容旧版：直接获取多条 AI 分析记录
      const aiData = await getDiaryAiListApi(curDay.value);
      const tagList = await getTagListApi();
      
      const processedAiData = aiData
        .map((item) => {
          const t = tagList.find((t) => t._id === item.parentId);
          if (t) {
            item.tag = t.content;
            return item;
          } else if (item.parentId === 'title') {
            // 标题
            title.value = item.content;
          }
        })
        .filter((item) => item != null);

      aiList.value = processedAiData;
    }

    // 初始化所有 AI 分析部分的显示/隐藏状态为 true（默认展开）
    aiList.value.forEach((_, index) => {
      aiSectionVisible[index] = true;
    });
  } else {
    // 完全重置 memoForm，确保没有残留内容
    memoForm.value = { content: '' };
    aiList.value = [];
  }
};

// 修改日记提交函数
const handleDiarySubmit = async ({ content, selectedTags, diaryId }) => {
  uni.vibrateShort();

  try {
    // AI 分析
    uni.showLoading({ title: 'AI 复盘中', mask: true });
    let { data } = await workflowApi('7484172363748130835', { 
      content, 
      question: selectedTags 
    });
    uni.hideLoading();
    
    const aiContent = JSON.parse(data.input);
    const tagsList = Object.keys(aiContent).filter(key => key !== '标题');
    
    // 使用组件内方法保存日记和分析
    await saveDiaryWithAnalysis({
      content,
      date: dayjs(curDay.value).format('YYYY-MM-DD'),
      diaryId,
      aiAnalysis: aiContent,
      tagsList
    });
    
    // 兼容性处理：同时保存旧版格式（可选，待所有功能完全迁移后可移除）
    const needBackwardCompatibility = true; // 根据需要设置是否需要兼容旧版
    if (needBackwardCompatibility) {
      await saveOldFormatAiAnalysis(aiContent, curDay.value);
    }
    
    uni.showToast({ title: '保存成功', icon: 'success' });
    showInputPopup.value = false;
    
    // 重新获取完整数据
    getDay(curDay.value);
  } catch (error) {
    console.error(error);
    uni.showToast({ title: '保存失败', icon: 'error' });
  }
};

// 兼容旧版的保存函数
const saveOldFormatAiAnalysis = async (aiContent, date) => {
  // 删除旧的 AI 分析结果
  await delMemoApi(`type == "aiDiary" && date == "${date}"`);
  
  // 保存新的 AI 分析结果（多条数据）
  const tagList = await getTagListApi();
  for (const key in aiContent) {
    // 按标签分别保存 AI 分析结果
    if (key === '标题') {
      await addDiaryApi({ 
        content: aiContent[key], 
        parentId: 'title', 
        type: 'aiDiary', 
        date
      });
    } else if (tagList.find(t => t.content === key)) {
      await addDiaryApi({ 
        content: aiContent[key], 
        parentId: tagList.find(t => t.content === key)._id, 
        type: 'aiDiary', 
        date
      });
    }
  }
};
```

### 4. 在 App.vue 的应用初始化中集成迁移逻辑

在 `src/common/appInit.js` 中添加数据迁移的调用逻辑，确保在应用启动时执行：

```javascript
// src/common/appInit.js
import { migrateDiaryData, checkAndMigrate } from '@/scripts/migrateDiaryData.js'

export default async function () {
  // 现有初始化代码...
  
  // 初始化 appVersion（仅 app 生效）
  initAppVersion()
  
  // 初始化分类数据库
  initCategoryDB()
  
  // 执行日记数据迁移
  await checkAndMigrate()
  
  // 其他现有代码...
}
```

这样实现有以下优势：
1. 只在应用启动时执行一次迁移检查
2. 利用已有的应用初始化流程
3. 迁移完成后方便移除迁移代码
4. 不会影响应用的其他功能初始化

在 `src/scripts/migrateDiaryData.js` 中导出相关函数：

```javascript
// src/scripts/migrateDiaryData.js
export const migrateDiaryData = async () => {
  // 现有迁移逻辑...
};

export const checkAndMigrate = async () => {
  // 直接弹窗询问用户是否升级
  uni.showModal({
    title: '数据升级提示',
    content: '检测到日记模块需要升级数据结构，是否立即升级？',
    success: async (res) => {
      if (res.confirm) {
        // 用户确认升级
        uni.showLoading({ title: '正在升级数据，请稍候...', mask: true });
        
        try {
          // 执行迁移
          const result = await migrateDiaryData();
          
          if (result.success) {
            // 更新版本记录
            uni.setStorageSync('app_version', '2.0.0');
            uni.hideLoading();
            uni.showToast({ title: '数据升级完成', icon: 'success' });
          } else {
            uni.hideLoading();
            uni.showModal({
              title: '数据升级失败',
              content: '请联系客服处理',
              showCancel: false
            });
          }
        } catch (e) {
          uni.hideLoading();
          uni.showModal({
            title: '数据升级异常',
            content: '请重启应用或联系客服',
            showCancel: false
          });
        }
      } else {
        // 用户取消升级
        uni.showToast({ title: '您取消了数据升级', icon: 'none' });
      }
    }
  });
};

// 可选：添加清理迁移相关数据的功能
export const cleanupMigration = async () => {
  // 待所有功能都确认正常后，可以清理旧版数据
  try {
    // 删除所有类型为 aiDiary 的记录
    await db.table('memo').where('type == "aiDiary"').delete();
    console.log('旧版 AI 分析数据清理完成');
    return { success: true };
  } catch (error) {
    console.error('清理旧数据失败：', error);
    return { success: false, error };
  }
};
```

## 注意事项

1. **数据一致性**：确保迁移过程中不丢失数据
2. **兼容性**：保持向下兼容，确保旧版功能正常运行，不修改现有 API 接口
3. **错误处理**：添加适当的错误处理和回滚机制
4. **性能优化**：使用批量操作减少数据库交互
5. **监控与日志**：添加日志记录，便于问题排查
6. **用户体验**：在迁移过程中提供友好的用户提示
7. **灰度发布**：考虑先对小部分用户进行升级测试
8. **迁移后清理**：待确认新版本稳定后，可选择性清理旧数据

实施这些更改后，日记功能将具有更好的性能、数据一致性和扩展性，同时保持现有功能的稳定性。
