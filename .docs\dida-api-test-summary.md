# 滴答清单API测试接口总结

## 测试页面位置
`src/pages/speak/index.vue` - 在"临时测试按钮"区域

## 测试参数
所有测试接口使用统一的测试参数：
- **projectId**: `5a9649f19fcffccade184229`
- **taskId**: `543246ffa5d7d971f6738b12`

## 已添加的测试接口

### 任务相关接口 (6个)

#### 1. 获取任务列表 (`testGetTasks`)
- **接口**: `getTasks`
- **参数**: 
  ```javascript
  {
    projectId: '5a9649f19fcffccade184229',
    completed: false,
    limit: 10
  }
  ```
- **功能**: 获取指定项目的未完成任务列表

#### 2. 创建任务 (`testCreateTask`)
- **接口**: `createTask`
- **参数**:
  ```javascript
  {
    taskData: {
      title: `测试任务 - ${new Date().toLocaleString()}`,
      content: '这是一个通过API创建的测试任务',
      projectId: '5a9649f19fcffccade184229',
      priority: 0,
      isAllDay: false
    }
  }
  ```
- **功能**: 在指定项目中创建新任务

#### 3. 获取单条任务 (`testGetTask`)
- **接口**: `getTask`
- **参数**:
  ```javascript
  {
    projectId: '5a9649f19fcffccade184229',
    taskId: '543246ffa5d7d971f6738b12'
  }
  ```
- **功能**: 获取指定任务的详细信息

#### 4. 更新任务 (`testUpdateTask`)
- **接口**: `updateTask`
- **参数**:
  ```javascript
  {
    taskId: '543246ffa5d7d971f6738b12',
    taskData: {
      id: '543246ffa5d7d971f6738b12',
      projectId: '5a9649f19fcffccade184229',
      title: `更新的任务标题 - ${new Date().toLocaleString()}`,
      content: '这是通过API更新的任务内容',
      priority: 1
    }
  }
  ```
- **功能**: 更新指定任务的信息

#### 5. 删除任务 (`testDeleteTask`)
- **接口**: `deleteTask`
- **参数**:
  ```javascript
  {
    projectId: '5a9649f19fcffccade184229',
    taskId: '543246ffa5d7d971f6738b12'
  }
  ```
- **功能**: 删除指定任务

#### 6. 完成任务 (`testCompleteTask`)
- **接口**: `completeTask`
- **参数**:
  ```javascript
  {
    projectId: '5a9649f19fcffccade184229',
    taskId: '543246ffa5d7d971f6738b12'
  }
  ```
- **功能**: 标记指定任务为完成状态

### 项目相关接口 (6个)

#### 7. 获取项目列表 (`testGetProjects`)
- **接口**: `getProjects`
- **参数**: 无
- **功能**: 获取当前用户的所有项目列表

#### 8. 获取项目详情 (`testGetProject`)
- **接口**: `getProject`
- **参数**:
  ```javascript
  {
    projectId: '5a9649f19fcffccade184229'
  }
  ```
- **功能**: 获取指定项目的详细信息

#### 9. 获取项目数据 (`testGetProjectData`)
- **接口**: `getProjectData`
- **参数**:
  ```javascript
  {
    projectId: '5a9649f19fcffccade184229'
  }
  ```
- **功能**: 获取项目信息、任务列表和列信息

#### 10. 创建项目 (`testCreateProject`)
- **接口**: `createProject`
- **参数**:
  ```javascript
  {
    projectData: {
      name: `测试项目 - ${new Date().toLocaleString()}`,
      color: '#4CAF50',
      viewMode: 'list',
      kind: 'TASK'
    }
  }
  ```
- **功能**: 创建新项目

#### 11. 更新项目 (`testUpdateProject`)
- **接口**: `updateProject`
- **参数**:
  ```javascript
  {
    projectId: '5a9649f19fcffccade184229',
    projectData: {
      name: `更新的项目名称 - ${new Date().toLocaleString()}`,
      color: '#FF9800',
      viewMode: 'kanban'
    }
  }
  ```
- **功能**: 更新指定项目的信息

#### 12. 删除项目 (`testDeleteProject`)
- **接口**: `deleteProject`
- **参数**:
  ```javascript
  {
    projectId: '5a9649f19fcffccade184229'
  }
  ```
- **功能**: 删除指定项目

## 测试说明

### 使用方法
1. 打开应用，导航到"选择训练模式"页面
2. 滚动到页面底部的"临时测试按钮"区域
3. 点击相应的测试按钮进行接口测试
4. 查看控制台输出和Toast提示获取测试结果

### 注意事项
1. **测试顺序**: 建议先测试获取类接口（如获取项目列表、获取任务列表），确认数据结构
2. **数据依赖**: 某些接口需要真实存在的数据ID，如果测试参数对应的数据不存在，会返回404错误
3. **权限验证**: 所有接口都需要有效的token，当前使用预设token进行测试
4. **错误处理**: 每个测试方法都包含完整的错误处理，失败时会在控制台显示详细错误信息

### 测试结果查看
- **成功**: Toast显示"操作成功，请看控制台"，控制台输出详细响应数据
- **失败**: Toast显示"操作失败，请看控制台"，控制台输出错误信息

### 建议测试流程
1. 首先测试 `testGetProjects` 获取项目列表，确认可用的项目ID
2. 使用真实项目ID测试 `testGetProjectData` 获取项目数据
3. 测试任务相关接口，使用真实的projectId和taskId
4. 最后测试创建、更新、删除等修改类接口

所有测试接口已完整实现，可以全面验证滴答清单API的功能。
