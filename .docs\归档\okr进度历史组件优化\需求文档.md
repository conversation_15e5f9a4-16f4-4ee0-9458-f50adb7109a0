# OKR 进度历史组件优化需求

status: draft

## 背景

当前 OKR 系统中的进度历史功能直接集成在关键结果详情页（krDetail.vue）中，展示方式较为简单，仅为时间线形式展示，缺乏筛选和过滤能力。随着用户记录的进度数据增多，查找和浏览特定时间段的进度记录变得困难。为提升用户体验，需要将进度历史记录功能进行组件化封装，并增强其筛选和查看能力。

## 需求

### 功能需求

1. **组件封装**
   - 将 krDetail.vue 中的进度历史记录功能抽取封装为独立组件
   - 组件需放置在 src/pages/okr/components 目录下

2. **筛选功能**
   - 在组件顶部添加筛选条件区域
   - 左侧显示当前选中月份（YYYY-MM 格式）
   - 月份两侧各有一个切换按钮，点击可左右切换月份
   - 右侧添加"按天/按月"的切换按钮，用于切换数据展示模式

3. **按天筛选模式**
   - 在筛选条件下方显示日历组件（类似于 z-calendar.vue）
   - 日历组件仅支持左右滑动切换周历，不需要展开显示整个月份的功能
   - 点击日历中的某一天，下方展示该天的进度记录列表
   - 当天有记录的日期需要特殊标记

4. **按月筛选模式**
   - 直接展示当前选中月份的全部进度记录列表
   - 按时间倒序排列

5. **记录展示**
   - 保留原有的时间线展示形式
   - 每条记录包含：日期、进度值、变化值、备注内容
   - 支持编辑和删除功能

### 非功能需求

1. **用户体验**
   - 筛选条件操作便捷直观
   - 有适当的加载反馈和空状态展示

## 技术方案

### 实现思路

1. **组件结构设计**
   ```
   l-progress-history.vue
   ├── 筛选条件区域
   │   ├── 月份显示及切换
   │   └── 展示模式切换
   ├── 日历组件（按天模式）
   └── 进度记录列表
   ```

2. **数据结构**
   ```javascript
   {
     filterMonth: '2025-05', // 当前筛选月份
     filterMode: 'day', // 'day'或'month'
     selectedDate: '2025-05-24', // 按天模式下选中的日期
     records: [], // 进度记录数组
     filteredRecords: [], // 筛选后的记录
     dateHasRecord: [] // 有记录的日期数组，用于日历标记
   }
   ```

3. **主要功能实现**
   - 封装现有的进度记录时间线组件
   - 基于 z-calendar.vue 组件定制简化版日历组件
   - 实现数据筛选逻辑

### 架构设计

```mermaid
graph TD
    A[krDetail.vue] --> B[l-progress-history.vue]
    B --> C[筛选条件组件]
    B --> D[简化版日历组件]
    B --> E[进度记录列表组件]
    C --> F[月份切换]
    C --> G[展示模式切换]
    D --> H[日期选择]
    E --> I[记录展示]
    E --> J[编辑/删除功能]
```

### 技术栈与约束
- UI 组件：复用现有组件样式
- 数据处理：dayjs 处理日期操作
- 交互约束：遵循现有应用的交互模式

## 风险评估

### 假设与未知因素
- 假设进度记录数据量不会过大（单个关键结果记录数不超过 200 条）
- 假设日历组件能够简化复用，不需要完全重写
