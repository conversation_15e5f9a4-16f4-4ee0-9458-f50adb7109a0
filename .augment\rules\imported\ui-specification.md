---
type: 'manual'
---

# UI 规范指南

为了确保整个项目的视觉统一性、可维护性以及未来主题的灵活性，在开发或修改 UI 组件时，请严格遵守以下关于 CSS 变量的指南：

1.  **检查现有变量**：

    - 在定义任何新的 CSS 颜色、间距、阴影、字体、圆角或类似的样式相关变量之前，请首先仔细检查全局样式文件 [variables.css](mdc:src/styles/variables.css)，确认是否已存在可复用的变量。
    - 优先复用现有变量，这有助于保持应用整体视觉风格的一致性。

2.  **添加新变量到 `variables.css`**：
    - **适用场景**：仅当所需样式值具备全局复用性（例如，用于定义应用的基础主题、通用 UI 元素、布局规范等）时，才应将其添加为 [variables.css](mdc:src/styles/variables.css) 中的全局 CSS 变量。
    - **命名规范**：新添加的变量名必须遵循 `kebab-case` 格式（例如 `--button-background-primary`），并能清晰、准确地描述其用途和代表的样式属性。
    - **组织与分类**：在 [variables.css](mdc:src/styles/variables.css) 中添加新变量时，请将其放置在相关的类别注释下（例如 `/* Colors */`, `/* Spacing */`, `/* Typography */`, `/* Shadows */` 等），以便于查找和管理。
    - **何时不添加**：对于仅在单一组件内部使用、不具备跨组件复用性的特定样式值，应优先考虑使用组件内部的局部 CSS 变量、SCSS/Sass 变量或直接的样式属性，避免不必要的全局污染。
