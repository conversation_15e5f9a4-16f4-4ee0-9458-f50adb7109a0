# 滴答清单 API 云函数

这个云函数封装了滴答清单的相关 API 接口，提供统一的调用方式。

## 功能列表

### 1. 密码登录 (passwordLogin)

使用用户名（邮箱或手机号）和密码进行滴答清单登录认证。

#### 参数

| 参数名   | 类型   | 必需 | 说明                     |
|----------|--------|------|--------------------------|
| username | string | 是   | 登录账户（邮箱或手机号） |
| password | string | 是   | 登录密码                 |

#### 返回值

**成功响应:**
```json
{
  "errCode": null,
  "errMsg": "登录成功",
  "data": {
    "token": "用户认证令牌",
    "userId": "用户唯一ID",
    "userCode": "用户代码",
    "username": "用户名",
    "inboxId": "默认任务添加清单ID",
    "pro": true,
    "teamPro": false,
    "proStartDate": "专业版开始日期",
    "proEndDate": "专业版结束日期",
    "subscribeType": "订阅类型",
    "subscribeFreq": "订阅频率",
    "needSubscribe": false,
    "teamUser": false,
    "activeTeamUser": false,
    "freeTrial": false,
    "gracePeriod": false,
    "ds": false
  }
}
```

**错误响应:**
```json
{
  "errCode": "错误代码",
  "errMsg": "错误描述",
  "details": "详细错误信息（可选）"
}
```

#### 错误代码说明

| 错误代码 | 说明 |
|----------|------|
| PARAM_IS_NULL | 参数为空 |
| API_ERROR | API 请求失败 |
| PARSE_ERROR | 响应数据解析失败 |
| LOGIN_FAILED | 登录失败（用户名或密码错误） |
| REQUEST_TIMEOUT | 请求超时 |
| UNAUTHORIZED | 认证失败 |
| FORBIDDEN | 账户被禁用或权限不足 |
| SERVER_ERROR | 服务器错误 |
| UNKNOWN_ERROR | 未知错误 |

## 使用示例

### 前端调用示例

```javascript
// 在 Vue 组件中调用
export default {
  methods: {
    async loginToDida() {
      try {
        const didaApi = uniCloud.importObject('dida-api')
        const result = await didaApi.passwordLogin('<EMAIL>', 'your-password')
        
        if (result.errCode) {
          // 登录失败
          uni.showToast({
            title: result.errMsg,
            icon: 'none'
          })
          return
        }
        
        // 登录成功
        const userInfo = result.data
        console.log('登录成功，用户信息:', userInfo)
        
        // 保存 token 用于后续 API 调用
        uni.setStorageSync('dida_token', userInfo.token)
        uni.setStorageSync('dida_user_info', userInfo)
        
        uni.showToast({
          title: '登录成功',
          icon: 'success'
        })
        
      } catch (error) {
        console.error('调用云函数失败:', error)
        uni.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        })
      }
    }
  }
}
```

### 云函数测试示例

```javascript
// 在其他云函数中调用
const didaApi = uniCloud.importObject('dida-api')

async function testLogin() {
  const result = await didaApi.passwordLogin('<EMAIL>', 'password123')
  console.log('登录结果:', result)
  return result
}
```

## 注意事项

1. **安全性**: 请确保在生产环境中妥善保护用户的登录凭据
2. **错误处理**: 建议在前端对所有可能的错误代码进行适当的处理
3. **Token 管理**: 登录成功后返回的 token 需要妥善保存，用于后续的 API 调用
4. **超时设置**: 当前设置的请求超时时间为 10 秒，可根据实际需要调整
5. **日志记录**: 云函数会记录关键的调用日志，便于问题排查

## 后续扩展

可以基于这个云函数继续添加其他滴答清单 API 接口，如：
- 获取任务列表
- 创建任务
- 更新任务
- 删除任务
- 获取项目列表
- 统计信息等

每个新接口都应该遵循相同的错误处理和返回格式规范。
