# 任务：创建通用训练记录弹窗组件

- **任务ID**: `task-keyword-story-01`
- **所属功能模块**: 语音训练 - 关键词讲故事
- **优先级**: 高
- **状态**: 未开始

---

## 1. 任务描述
创建一个通用的、可复用的Vue组件，用于以弹窗形式展示训练记录列表。此组件在当前阶段将完全使用**模拟数据 (mock data)** 来进行开发和展示，无需任何数据库或API调用。

根据项目命名规范，由于这是一个通用的全局组件，建议命名为 `z-record-list-popup` 并放置在 `src/components/` 目录下。

## 2. 技术实现详情
### 组件创建
-   **位置**: `src/pages/speak/components/l-record-list-popup.vue`
-   **命名**: `l-record-list-popup`

### Props 定义
组件需要接收以下 `props`:
-   `show (v-model)`: `Boolean` - 控制弹窗的显示与隐藏。
-   `title`: `String` - 弹窗的标题，默认为 "训练记录"。
-   `records`: `Array` - 需要展示的记录列表。

### Events 定义
组件需要触发以下 `events`:
-   `select-item`: 当用户点击列表中的某一条记录时触发，并回传被点击的 `record` 对象。
-   `close`: 当弹窗关闭时触发。

### 内部实现
-   使用 `uni-popup` 或类似的基础组件作为弹窗容器。
-   循环渲染 `records` prop 传入的数组。
-   列表项的UI应至少包含：
    -   记录日期
    -   关键词（以标签形式展示）
    -   总体评价摘要
-   为列表项添加点击事件，触发 `select-item` event。
-   提供一个清晰的关闭按钮。

### 模拟数据 (`mock data`)
在组件内部或传入的 `prop` 中，使用以下结构的模拟数据进行UI开发和测试：
```javascript
[
  {
    _id: 'record-1',
    title: '宇宙, 冒险, 机器人',
    createTime: '2023-10-27T10:00:00Z',
    content: JSON.stringify({
      type: 'keywordStory',
      keywords: ['宇宙', '冒险', '机器人'],
      evaluation: {
        id: 'eval-1',
        summaryText: '故事非常有创意，关键词使用得很好，但在结尾处稍显仓促。'
      }
    })
  },
  {
    _id: 'record-2',
    title: '城堡, 秘密, 巨龙',
    createTime: '2023-10-26T15:30:00Z',
    content: JSON.stringify({
      type: 'keywordStory',
      keywords: ['城堡', '秘密', '巨龙'],
      evaluation: {
        id: 'eval-2',
        summaryText: '叙事流畅，但巨龙的角色塑造可以更丰满一些。'
      }
    })
  }
]
```
> **注意**: 在渲染时，需要 `JSON.parse(record.content)` 来获取内部数据。

## 3. 验收标准
-   [ ] 成功创建 `z-record-list-popup.vue` 文件。
-   [ ] 组件可以通过 `v-model:show` 控制显示和隐藏。
-   [ ] 组件能够正确接收 `records` 数组并渲染出列表，UI样式应清晰美观。
-   [ ] 列表项展示了正确的模拟数据（日期、关键词、摘要）。
-   [ ] 点击列表项时，`select-item` 事件被正确触发并回传数据。
-   [ ] 点击关闭按钮或遮罩层可以关闭弹窗。
-   [ ] 组件在 `keyword-story-page.vue` 中被引入后，能够通过一个按钮成功唤起并展示模拟数据。 