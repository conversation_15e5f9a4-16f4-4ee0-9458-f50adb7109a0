# 滴答清单 API 云函数部署指南

## 概述

这个云函数封装了滴答清单的密码登录功能，提供统一的 API 接口供前端调用。

## 文件结构

```
dida-api/
├── index.obj.js          # 主要的云函数代码
├── package.json          # 依赖配置
├── README.md            # 功能说明文档
├── DEPLOYMENT.md        # 部署指南（本文件）
└── test.js              # 本地测试脚本
```

## 部署步骤

### 1. 检查云函数代码

确保 `index.obj.js` 文件包含完整的密码登录功能：
- 参数验证
- HTTP 请求处理
- 错误处理
- 响应格式化

### 2. 上传云函数

在 HBuilderX 中：
1. 右键点击 `uniCloud-aliyun/cloudfunctions/dida-api` 目录
2. 选择 "上传并运行云函数"
3. 等待上传完成

或者使用命令行：
```bash
# 进入项目根目录
cd d:\zcjFile\okr-web

# 使用 uniCloud CLI 上传
unicloud deploy --target aliyun --type cloudfunctions --name dida-api
```

### 3. 验证部署

部署完成后，可以通过以下方式验证：

#### 方式1：在 HBuilderX 控制台测试
1. 右键点击云函数目录
2. 选择 "本地运行云函数"
3. 在弹出的测试界面中输入测试参数

#### 方式2：在前端页面测试
访问 `pages/dida/login` 页面，尝试登录功能。

#### 方式3：使用本地测试脚本
```bash
# 在云函数目录下运行
cd uniCloud-aliyun/cloudfunctions/dida-api
node test.js
```

## 前端集成

### 1. 导入 API 模块

在需要使用的页面中导入：
```javascript
import { didaPasswordLogin } from '@/api/dida.js'
```

### 2. 调用登录接口

```javascript
try {
  const userInfo = await didaPasswordLogin('username', 'password')
  console.log('登录成功:', userInfo)
  // 处理登录成功逻辑
} catch (error) {
  console.error('登录失败:', error.message)
  // 处理登录失败逻辑
}
```

### 3. 页面路由

确保在 `src/pages.json` 中已添加登录页面路由：
```json
{
  "path": "pages/dida/login",
  "style": {
    "enablePullDownRefresh": false,
    "navigationBarTitleText": "滴答清单登录",
    "navigationStyle": "custom"
  }
}
```

## 配置说明

### 环境变量

如果需要配置特定的环境变量，可以在云函数中添加：
```javascript
const config = {
  didaApiUrl: process.env.DIDA_API_URL || 'https://api.dida365.com',
  timeout: process.env.REQUEST_TIMEOUT || 10000
}
```

### 请求头配置

当前使用的请求头：
```javascript
headers: {
  'Content-Type': 'application/json',
  'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
}
```

如需修改，请在 `index.obj.js` 中调整相应配置。

## 监控和日志

### 查看云函数日志

1. 在 HBuilderX 中打开 uniCloud 控制台
2. 选择对应的服务空间
3. 进入"云函数"页面
4. 点击 `dida-api` 查看运行日志

### 关键日志点

云函数会记录以下关键信息：
- 登录请求开始
- API 响应状态和数据
- 错误信息和异常堆栈

## 故障排除

### 常见问题

1. **部署失败**
   - 检查网络连接
   - 确认服务空间配置正确
   - 检查代码语法错误

2. **登录失败**
   - 验证用户名和密码是否正确
   - 检查滴答清单 API 是否可访问
   - 查看云函数运行日志

3. **请求超时**
   - 检查网络连接
   - 考虑增加超时时间
   - 确认滴答清单服务状态

### 调试技巧

1. 启用详细日志：在云函数中添加更多 `console.log`
2. 使用本地测试脚本验证逻辑
3. 检查 uniCloud 控制台的错误报告

## 安全注意事项

1. **敏感信息保护**
   - 不要在日志中输出完整的密码
   - 考虑对敏感数据进行脱敏处理

2. **请求频率限制**
   - 考虑添加请求频率限制
   - 防止恶意攻击和滥用

3. **错误信息**
   - 避免在错误响应中暴露过多系统信息
   - 提供用户友好的错误提示

## 后续扩展

基于当前的基础架构，可以继续添加：
- 获取任务列表接口
- 创建/更新/删除任务接口
- 获取项目列表接口
- 统计数据接口
- 其他滴答清单 API 功能

每个新接口都应该遵循相同的错误处理和响应格式规范。
