/**
 * AI助手V1.0工具注册系统集成测试
 * 测试chatStreamSSE函数与工具注册系统的集成
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals'

// Mock uniCloud
global.uniCloud = {
  deserializeSSEChannel: jest.fn(() => ({
    write: jest.fn(),
    end: jest.fn()
  }))
}

// Mock OpenAI
const mockOpenAI = {
  chat: {
    completions: {
      create: jest.fn()
    }
  }
}

jest.mock('openai', () => {
  return jest.fn(() => mockOpenAI)
})

// 模拟AI助手模块的核心功能
const TOOL_REGISTRY = {
  getProjects: {
    name: 'getProjects',
    description: '获取滴答清单中的所有项目',
    usage: '当用户想要查看、搜索项目时使用',
    parameters: {
      filter: {
        type: 'string',
        required: false,
        default: '',
        description: '项目名称筛选关键词'
      }
    },
    metadata: {
      priority: 0.8,
      category: 'data_retrieval'
    }
  },
  getTasks: {
    name: 'getTasks',
    description: '获取指定项目下的任务列表',
    usage: '当用户想要查看、搜索、查询任务时使用',
    parameters: {
      projectId: {
        type: 'string',
        required: true,
        description: '项目ID，必须是有效的项目标识符'
      }
    },
    metadata: {
      priority: 0.9,
      category: 'data_retrieval'
    }
  }
}

function generateToolPrompt(toolRegistry) {
  let toolPrompt = '你可以使用以下工具来帮助用户完成任务：\n\n'

  for (const [toolKey, tool] of Object.entries(toolRegistry)) {
    toolPrompt += `**${tool.name}** (${toolKey})\n`
    toolPrompt += `- 功能：${tool.description}\n`
    toolPrompt += `- 使用场景：${tool.usage}\n`
    toolPrompt += `- 优先级：${tool.metadata?.priority || 'N/A'}\n`
    toolPrompt += `- 参数：\n`

    if (tool.parameters) {
      for (const [paramName, param] of Object.entries(tool.parameters)) {
        const required = param.required ? '必需' : '可选'
        const defaultValue = param.default ? ` (默认: ${param.default})` : ''
        toolPrompt += `  - ${paramName} (${param.type}, ${required}${defaultValue}): ${param.description}\n`
      }
    }
    
    toolPrompt += '\n'
  }

  return toolPrompt
}

// 模拟chatStreamSSE函数的核心逻辑
async function mockChatStreamSSE(params) {
  const toolPrompt = generateToolPrompt(TOOL_REGISTRY)
  
  let {
    message,
    messages: history_records = [],
    model = 'doubao-seed-1-6-250615',
    system = `你是一个专业的任务分析助手。你的主要职责是理解用户输入的内容，并判断用户的意图类型。

${toolPrompt}

请分析用户输入内容，并将其归类为以下两种意图之一：
1. task: 当用户想要执行任何与任务相关的操作时（包括创建、查询、修改、删除任务等）
2. chat: 其他所有不属于任务操作的内容，视为一般闲聊对话

分析完成后，必须严格按照以下格式输出结果：
- 如果是 task 类型，只输出：「意图类型」：task
- 如果是 chat 类型，输出：
  「意图类型」：chat
  「闲聊回复」：[针对用户问题的回复内容]

注意：
- 分析要准确，不要混淆不同意图类型
- 只输出上述指定的两行内容，不要添加任何其他解释或内容
- 确保格式严格遵循示例，包括使用中文引号「」
- 虽然现在有可用的工具，但在V1.0版本中暂不执行工具调用，仅进行意图识别。`,
    channel,
  } = params

  // 参数校验
  if (!message) {
    return {
      errCode: 'PARAM_IS_NULL',
      errMsg: '消息内容不能为空'
    }
  }

  if (!channel) {
    return {
      errCode: 'PARAM_IS_NULL',
      errMsg: 'SSE Channel 不能为空'
    }
  }

  // 验证系统提示词包含工具信息
  const hasToolInfo = system.includes('getProjects') && system.includes('getTasks')
  
  return {
    errCode: 0,
    errMsg: 'success',
    data: {
      type: 'stream_complete',
      content: '模拟AI回复',
      intentType: 'find_task',
      totalChunks: 1,
      systemPromptContainsTools: hasToolInfo,
      toolPromptGenerated: toolPrompt.length > 0
    }
  }
}

describe('AI助手V1.0工具注册系统集成测试', () => {
  let mockChannel

  beforeEach(() => {
    mockChannel = {
      write: jest.fn(),
      end: jest.fn()
    }
    
    // 重置所有mock
    jest.clearAllMocks()
    
    // 设置uniCloud mock
    global.uniCloud.deserializeSSEChannel.mockReturnValue(mockChannel)
  })

  describe('chatStreamSSE集成测试', () => {
    it('应该成功处理带有工具信息的请求', async () => {
      const params = {
        message: '查看我的项目列表',
        channel: 'mock-channel'
      }

      const result = await mockChatStreamSSE(params)
      
      expect(result.errCode).toBe(0)
      expect(result.data.systemPromptContainsTools).toBe(true)
      expect(result.data.toolPromptGenerated).toBe(true)
    })

    it('应该在系统提示词中包含工具描述', async () => {
      const params = {
        message: '帮我创建一个任务',
        channel: 'mock-channel'
      }

      const result = await mockChatStreamSSE(params)
      
      expect(result.errCode).toBe(0)
      expect(result.data.systemPromptContainsTools).toBe(true)
    })

    it('应该正确处理参数验证', async () => {
      // 测试缺少message参数
      const paramsWithoutMessage = {
        channel: 'mock-channel'
      }

      const result1 = await mockChatStreamSSE(paramsWithoutMessage)
      expect(result1.errCode).toBe('PARAM_IS_NULL')
      expect(result1.errMsg).toContain('消息内容不能为空')

      // 测试缺少channel参数
      const paramsWithoutChannel = {
        message: '测试消息'
      }

      const result2 = await mockChatStreamSSE(paramsWithoutChannel)
      expect(result2.errCode).toBe('PARAM_IS_NULL')
      expect(result2.errMsg).toContain('SSE Channel 不能为空')
    })

    it('应该生成包含所有工具信息的提示词', async () => {
      const toolPrompt = generateToolPrompt(TOOL_REGISTRY)
      
      // 验证包含工具名称
      expect(toolPrompt).toContain('getProjects')
      expect(toolPrompt).toContain('getTasks')
      
      // 验证包含工具描述
      expect(toolPrompt).toContain('获取滴答清单中的所有项目')
      expect(toolPrompt).toContain('获取指定项目下的任务列表')
      
      // 验证包含优先级信息
      expect(toolPrompt).toContain('优先级：0.8')
      expect(toolPrompt).toContain('优先级：0.9')
      
      // 验证包含参数信息
      expect(toolPrompt).toContain('filter (string, 可选)')
      expect(toolPrompt).toContain('projectId (string, 必需)')
    })

    it('应该保持V1.0版本的功能边界', async () => {
      const params = {
        message: '查看OKR项目的任务',
        channel: 'mock-channel'
      }

      const result = await mockChatStreamSSE(params)
      
      // V1.0版本应该只进行意图识别，不执行工具调用
      expect(result.errCode).toBe(0)
      expect(result.data.type).toBe('stream_complete')
      
      // 确认系统提示词包含V1.0版本的说明
      const toolPrompt = generateToolPrompt(TOOL_REGISTRY)
      const systemPrompt = `你是一个专业的任务分析助手。你的主要职责是理解用户输入的内容，并判断用户的意图类型。

${toolPrompt}

请分析用户输入内容，并将其归类为以下三种意图之一：
1. create_task: 当用户想要创建、添加、设置、安排一个新任务时
2. find_task: 当用户想要查询、搜索、查看已有的任务时
3. chat: 其他所有不属于创建任务或查找任务的内容，视为一般闲聊

分析完成后，必须严格按照以下格式输出结果：
「意图类型」：[意图类型代码，必须是 create_task、find_task 或 chat 之一]
「意图内容」：[如果是创建任务，提取出要创建的任务内容；如果是查找任务，提取出要查找的任务关键词；如果是闲聊，则回复用户问题]

注意：
- 分析要准确，不要混淆不同意图类型
- 只输出上述指定的两行内容，不要添加任何其他解释或内容
- 确保格式严格遵循示例，包括使用中文引号「」
- 虽然现在有可用的工具，但在V1.0版本中暂不执行工具调用，仅进行意图识别。`
      
      expect(systemPrompt).toContain('虽然现在有可用的工具，但在V1.0版本中暂不执行工具调用，仅进行意图识别')
    })
  })
})
