import db from './database'
import { isUUID } from '@/utils/tools'

/**
 * 添加一条数据
 * @param params OKR 参数
 * @returns 新增 OKR 的 ID
 */
export const addOkrApi = async (params: API.CreateParams<DB.Okr>) => {
  return await db.table('okrObject').add(params)
}

/**
 * 修改一条数据
 * @param {string} id 更新 id
 * @param {object} params 更新参数
 */
export const updateOkrApi = async (id: string, params: API.UpdateParams<DB.Okr>) => {
  return await db.table('okrObject').update(id, params)
}

/**
 * 获取详情
 * @param {string} _id 查询 id
 */
export const getOkrApi = async (_id: string) => {
  try {
    const res = await db.table('okrObject').get(_id)
    if (res) {
      res.motivation = JSON.parse(res.motivation || '[]')
      res.feasibility = JSON.parse(res.feasibility || '[]')
      // 获取 okr 进度
      const tasks = await db.table('task').where(`okrId == "${_id}" && parentId == ""`).toArray()
      res.curVal = tasks.reduce((acc, task) => acc + (task.curVal || 0), 0) // 目标进度当前值
      res.tgtVal = tasks.reduce((acc, task) => acc + (task.tgtVal || 0), 0) // 目标进度目标值
      return res
    } else {
      return {}
    }
  } catch (error) {
    console.error(error)
    throw new Error(String(error))
  }
}
/**
 * 获取列表
 * @param {string} params 查询参数
 */
export const getOkrListApi = async (params?: string) => {
  try {
    const okrList = await db.table('okrObject').where(params).toArray()
    // 只处理目标本身，不处理任务和关键结果
    return okrList.map((item) => {
      item.motivation = JSON.parse(item.motivation || '[]')
      item.feasibility = JSON.parse(item.feasibility || '[]')
      return item
    })
  } catch (error) {
    console.error(error)
    throw new Error(String(error))
  }
}
/**
 * 删除 okr
 * @params 传入 _id 或者 筛选条件
 */
export const delOkrApi = async (params: string) => {
  try {
    if (isUUID(params)) {
      await db.table('okrObject').update(params, { deleteTime: new Date().toISOString() })
    } else {
      await db.table('okrObject').where(params).modify({ deleteTime: new Date().toISOString() })
    }
  } catch (error) {
    console.error(error)
    throw new Error(String(error))
  }
}
